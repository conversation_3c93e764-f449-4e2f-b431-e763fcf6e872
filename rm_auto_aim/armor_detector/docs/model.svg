<svg xmlns="http://www.w3.org/2000/svg" id="export" class="canvas" preserveAspectRatio="xMidYMid meet" style="" width="1656" height="55"><rect id="background" fill="#fff" pointer-events="all" width="1656" height="55"/><g id="origin" transform="translate(2.5, 2.5) scale(1)"><g id="clusters" class="clusters"/><g id="edge-paths" class="edge-paths"><defs><marker id="arrowhead" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto" style="fill: rgb(0, 0, 0);"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker><marker id="arrowhead-select" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto" style="fill: rgb(238, 0, 0);"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker><marker id="arrowhead-hover" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker></defs><path id="edge-input.1" class="edge-path" d="M49.34375,25L51.010416666666664,25C52.677083333333336,25,56.010416666666664,25,63.60374959309896,25C71.19708251953125,25,83.0504150390625,25,94.90374755859375,25C106.757080078125,25,118.61041259765625,25,126.20374552408855,25C133.79707845052084,25,137.13041178385416,25,138.79707845052084,25L140.4637451171875,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M49.34375,25L51.010416666666664,25C52.677083333333336,25,56.010416666666664,25,63.60374959309896,25C71.19708251953125,25,83.0504150390625,25,94.90374755859375,25C106.757080078125,25,118.61041259765625,25,126.20374552408855,25C133.79707845052084,25,137.13041178385416,25,138.79707845052084,25L140.4637451171875,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/conv1/Conv_output_0" class="edge-path" d="M215.4637451171875,25L221.39041137695312,25C227.31707763671875,25,239.17041015625,25,251.02374267578125,25C262.8770751953125,25,274.73040771484375,25,280.6570739746094,25L286.583740234375,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M215.4637451171875,25L221.39041137695312,25C227.31707763671875,25,239.17041015625,25,251.02374267578125,25C262.8770751953125,25,274.73040771484375,25,280.6570739746094,25L286.583740234375,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/act/Relu_output_0" class="edge-path" d="M322.974365234375,25L328.9010314941406,25C334.82769775390625,25,346.6810302734375,25,358.53436279296875,25C370.3876953125,25,382.24102783203125,25,388.1676940917969,25L394.0943603515625,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M322.974365234375,25L328.9010314941406,25C334.82769775390625,25,346.6810302734375,25,358.53436279296875,25C370.3876953125,25,382.24102783203125,25,388.1676940917969,25L394.0943603515625,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/pool1/MaxPool_output_0" class="edge-path" d="M451.4849853515625,25L457.4116516113281,25C463.33831787109375,25,475.191650390625,25,487.04498291015625,25C498.8983154296875,25,510.75164794921875,25,516.6783142089844,25L522.60498046875,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M451.4849853515625,25L457.4116516113281,25C463.33831787109375,25,475.191650390625,25,487.04498291015625,25C498.8983154296875,25,510.75164794921875,25,516.6783142089844,25L522.60498046875,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/conv2/Conv_output_0" class="edge-path" d="M601.76123046875,25L608.157896677653,25C614.554562886556,25,627.3478953043619,25,640.141227722168,25C652.934560139974,25,665.7278925577799,25,672.124558766683,25L678.5212249755859,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M601.76123046875,25L608.157896677653,25C614.554562886556,25,627.3478953043619,25,640.141227722168,25C652.934560139974,25,665.7278925577799,25,672.124558766683,25L678.5212249755859,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/act_1/Relu_output_0" class="edge-path" d="M714.9118499755859,25L721.3085161844889,25C727.705182393392,25,740.4985148111979,25,753.2918472290039,25C766.0851796468099,25,778.8785120646158,25,785.2751782735189,25L791.6718444824219,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M714.9118499755859,25L721.3085161844889,25C727.705182393392,25,740.4985148111979,25,753.2918472290039,25C766.0851796468099,25,778.8785120646158,25,785.2751782735189,25L791.6718444824219,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/pool2/MaxPool_output_0" class="edge-path" d="M849.0624694824219,25L854.4895528157552,25C859.9166361490885,25,870.7708028157552,25,881.6249694824219,25C892.4791361490885,25,903.3333028157552,25,908.7603861490885,25L914.1874694824219,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M849.0624694824219,25L854.4895528157552,25C859.9166361490885,25,870.7708028157552,25,881.6249694824219,25C892.4791361490885,25,903.3333028157552,25,908.7603861490885,25L914.1874694824219,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/flatten/Flatten_output_0" class="edge-path" d="M964.0468444824219,25L968.0935109456381,25C972.1401774088541,25,980.2335103352865,25,988.3268432617188,25C996.420176188151,25,1004.5135091145834,25,1008.5601755777994,25L1012.6068420410156,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M964.0468444824219,25L968.0935109456381,25C972.1401774088541,25,980.2335103352865,25,988.3268432617188,25C996.420176188151,25,1004.5135091145834,25,1008.5601755777994,25L1012.6068420410156,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/fc1/Gemm_output_0" class="edge-path" d="M1087.6068420410156,25L1091.6535085042317,25C1095.700174967448,25,1103.7935078938801,25,1111.8868408203125,25C1119.9801737467449,25,1128.073506673177,25,1132.1201731363933,25L1136.1668395996094,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1087.6068420410156,25L1091.6535085042317,25C1095.700174967448,25,1103.7935078938801,25,1111.8868408203125,25C1119.9801737467449,25,1128.073506673177,25,1132.1201731363933,25L1136.1668395996094,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/act_2/Relu_output_0" class="edge-path" d="M1172.5574645996094,25L1176.6041310628254,25C1180.6507975260417,25,1188.7441304524739,25,1196.8374633789062,25C1204.9307963053386,25,1213.0241292317708,25,1217.070795694987,25L1221.1174621582031,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1172.5574645996094,25L1176.6041310628254,25C1180.6507975260417,25,1188.7441304524739,25,1196.8374633789062,25C1204.9307963053386,25,1213.0241292317708,25,1217.070795694987,25L1221.1174621582031,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/fc2/Gemm_output_0" class="edge-path" d="M1296.1174621582031,25L1299.694128672282,25C1303.2707951863606,25,1310.4241282145183,25,1317.5774612426758,25C1324.7307942708333,25,1331.884127298991,25,1335.4607938130696,25L1339.0374603271484,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1296.1174621582031,25L1299.694128672282,25C1303.2707951863606,25,1310.4241282145183,25,1317.5774612426758,25C1324.7307942708333,25,1331.884127298991,25,1335.4607938130696,25L1339.0374603271484,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/act_3/Relu_output_0" class="edge-path" d="M1375.4280853271484,25L1379.0047518412273,25C1382.581418355306,25,1389.7347513834636,25,1396.888084411621,25C1404.0414174397786,25,1411.1947504679363,25,1414.771416982015,25L1418.3480834960938,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1375.4280853271484,25L1379.0047518412273,25C1382.581418355306,25,1389.7347513834636,25,1396.888084411621,25C1404.0414174397786,25,1411.1947504679363,25,1414.771416982015,25L1418.3480834960938,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-/fc3/Gemm_output_0" class="edge-path" d="M1493.3480834960938,25L1496.4547500610352,25C1499.5614166259766,25,1505.7747497558594,25,1511.9880828857422,25C1518.201416015625,25,1524.4147491455078,25,1527.5214157104492,25L1530.6280822753906,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1493.3480834960938,25L1496.4547500610352,25C1499.5614166259766,25,1505.7747497558594,25,1511.9880828857422,25C1518.201416015625,25,1524.4147491455078,25,1527.5214157104492,25L1530.6280822753906,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/><path id="edge-23" class="edge-path" d="M1586.850814819336,25L1589.9574813842773,25C1593.0641479492188,25,1599.2774810791016,25,1605.4908142089844,25C1611.7041473388672,25,1617.91748046875,25,1621.0241470336914,25L1624.1308135986328,25" style="stroke: rgb(0, 0, 0); stroke-width: 1px; fill: none; marker-end: url(&quot;#arrowhead&quot;);"/><path class="edge-path-hit-test" d="M1586.850814819336,25L1589.9574813842773,25C1593.0641479492188,25,1599.2774810791016,25,1605.4908142089844,25C1611.7041473388672,25,1617.91748046875,25,1621.0241470336914,25L1624.1308135986328,25" style="pointer-events: stroke; stroke-width: 0.5em; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0.001;"/></g><g id="edge-labels" class="edge-labels"><text class="edge-label" id="edge-label-edge-input.1" transform="translate(69.34375,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×1×28×28</tspan></text><text class="edge-label" id="edge-label-edge-/conv1/Conv_output_0" transform="translate(225.4637451171875,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×6×28×28</tspan></text><text class="edge-label" id="edge-label-edge-/act/Relu_output_0" transform="translate(332.974365234375,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×6×28×28</tspan></text><text class="edge-label" id="edge-label-edge-/pool1/MaxPool_output_0" transform="translate(461.4849853515625,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×6×14×14</tspan></text><text class="edge-label" id="edge-label-edge-/conv2/Conv_output_0" transform="translate(611.76123046875,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×16×10×10</tspan></text><text class="edge-label" id="edge-label-edge-/act_1/Relu_output_0" transform="translate(724.9118499755859,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×16×10×10</tspan></text><text class="edge-label" id="edge-label-edge-/pool2/MaxPool_output_0" transform="translate(859.0624694824219,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×16×5×5</tspan></text><text class="edge-label" id="edge-label-edge-/flatten/Flatten_output_0" transform="translate(974.0468444824219,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×400</tspan></text><text class="edge-label" id="edge-label-edge-/fc1/Gemm_output_0" transform="translate(1097.6068420410156,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×120</tspan></text><text class="edge-label" id="edge-label-edge-/act_2/Relu_output_0" transform="translate(1182.5574645996094,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×120</tspan></text><text class="edge-label" id="edge-label-edge-/fc2/Gemm_output_0" transform="translate(1306.1174621582031,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×84</tspan></text><text class="edge-label" id="edge-label-edge-/act_3/Relu_output_0" transform="translate(1385.4280853271484,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×84</tspan></text><text class="edge-label" id="edge-label-edge-/fc3/Gemm_output_0" transform="translate(1503.3480834960938,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×9</tspan></text><text class="edge-label" id="edge-label-edge-23" transform="translate(1596.850814819336,35)" style="opacity: 1; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 10px;"><tspan xml:space="preserve" dy="1em" x="1">1×9</tspan></text></g><g id="nodes" class="nodes"><g id="input-name-input.1" class="node graph-input" transform="translate(0,15)" style=""><g class="node-item graph-item-input" transform="translate(0,0)"><path d="M5,0h39.34375a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-39.34375a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(238, 238, 238); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none;">input.1</text><title>float32[1,1,28,28]</title></g><path class="node node-border" d="M5,0h39.34375a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-39.34375a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/conv1/Conv" class="node graph-node" transform="translate(140.4637451171875,0)" style=""><g class="node-item node-item-type node-item-type-layer" transform="translate(0,0)"><path d="M5,0h65a5,5 0 0 1 5,5v15a0,0 0 0 1 0,0h-75a0,0 0 0 1 0,0v-15a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 136); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Conv</text><title>/conv1/Conv</title></g><g class="node-attribute-list" transform="translate(0,20)"><path d="M0,0h75a0,0 0 0 1 0,0v25a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-25a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="12" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[6,1,5,5]</title><tspan style="font-weight: bold;">W</tspan><tspan>〈6×1×5×5〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="24" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[6]</title><tspan style="font-weight: bold;">B</tspan><tspan>〈6〉</tspan></text></g><line class="node" x1="0" x2="75" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h65a5,5 0 0 1 5,5v40a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-40a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/act/Relu" class="node graph-node" transform="translate(286.583740234375,15)" style=""><g class="node-item node-item-type node-item-type-activation" transform="translate(0,0)"><path d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(112, 41, 33); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Relu</text><title>/act/Relu</title></g><path class="node node-border" d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/pool1/MaxPool" class="node graph-node" transform="translate(394.0943603515625,15)" style=""><g class="node-item node-item-type node-item-type-pool" transform="translate(0,0)"><path d="M5,0h47.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-47.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 51); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">MaxPool</text><title>/pool1/MaxPool</title></g><path class="node node-border" d="M5,0h47.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-47.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/conv2/Conv" class="node graph-node" transform="translate(522.60498046875,0)" style=""><g class="node-item node-item-type node-item-type-layer" transform="translate(0,0)"><path d="M5,0h69.15625a5,5 0 0 1 5,5v15a0,0 0 0 1 0,0h-79.15625a0,0 0 0 1 0,0v-15a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 136); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Conv</text><title>/conv2/Conv</title></g><g class="node-attribute-list" transform="translate(0,20)"><path d="M0,0h79.15625a0,0 0 0 1 0,0v25a5,5 0 0 1 -5,5h-69.15625a5,5 0 0 1 -5,-5v-25a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="12" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[16,6,5,5]</title><tspan style="font-weight: bold;">W</tspan><tspan>〈16×6×5×5〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="24" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[16]</title><tspan style="font-weight: bold;">B</tspan><tspan>〈16〉</tspan></text></g><line class="node" x1="0" x2="79.15625" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h69.15625a5,5 0 0 1 5,5v40a5,5 0 0 1 -5,5h-69.15625a5,5 0 0 1 -5,-5v-40a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/act_1/Relu" class="node graph-node" transform="translate(678.5212249755859,15)" style=""><g class="node-item node-item-type node-item-type-activation" transform="translate(0,0)"><path d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(112, 41, 33); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Relu</text><title>/act_1/Relu</title></g><path class="node node-border" d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/pool2/MaxPool" class="node graph-node" transform="translate(791.6718444824219,15)" style=""><g class="node-item node-item-type node-item-type-pool" transform="translate(0,0)"><path d="M5,0h47.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-47.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 51); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">MaxPool</text><title>/pool2/MaxPool</title></g><path class="node node-border" d="M5,0h47.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-47.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/flatten/Flatten" class="node graph-node" transform="translate(914.1874694824219,15)" style=""><g class="node-item node-item-type node-item-type-shape" transform="translate(0,0)"><path d="M5,0h39.859375a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-39.859375a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(108, 79, 71); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Flatten</text><title>/flatten/Flatten</title></g><path class="node node-border" d="M5,0h39.859375a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-39.859375a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/fc1/Gemm" class="node graph-node" transform="translate(1012.6068420410156,0)" style=""><g class="node-item node-item-type node-item-type-layer" transform="translate(0,0)"><path d="M5,0h65a5,5 0 0 1 5,5v15a0,0 0 0 1 0,0h-75a0,0 0 0 1 0,0v-15a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 136); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Gemm</text><title>/fc1/Gemm</title></g><g class="node-attribute-list" transform="translate(0,20)"><path d="M0,0h75a0,0 0 0 1 0,0v25a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-25a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="12" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[120,400]</title><tspan style="font-weight: bold;">B</tspan><tspan>〈120×400〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="24" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[120]</title><tspan style="font-weight: bold;">C</tspan><tspan>〈120〉</tspan></text></g><line class="node" x1="0" x2="75" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h65a5,5 0 0 1 5,5v40a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-40a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/act_2/Relu" class="node graph-node" transform="translate(1136.1668395996094,15)" style=""><g class="node-item node-item-type node-item-type-activation" transform="translate(0,0)"><path d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(112, 41, 33); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Relu</text><title>/act_2/Relu</title></g><path class="node node-border" d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/fc2/Gemm" class="node graph-node" transform="translate(1221.1174621582031,0)" style=""><g class="node-item node-item-type node-item-type-layer" transform="translate(0,0)"><path d="M5,0h65a5,5 0 0 1 5,5v15a0,0 0 0 1 0,0h-75a0,0 0 0 1 0,0v-15a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 136); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Gemm</text><title>/fc2/Gemm</title></g><g class="node-attribute-list" transform="translate(0,20)"><path d="M0,0h75a0,0 0 0 1 0,0v25a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-25a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="12" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[84,120]</title><tspan style="font-weight: bold;">B</tspan><tspan>〈84×120〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="24" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[84]</title><tspan style="font-weight: bold;">C</tspan><tspan>〈84〉</tspan></text></g><line class="node" x1="0" x2="75" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h65a5,5 0 0 1 5,5v40a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-40a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/act_3/Relu" class="node graph-node" transform="translate(1339.0374603271484,15)" style=""><g class="node-item node-item-type node-item-type-activation" transform="translate(0,0)"><path d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(112, 41, 33); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Relu</text><title>/act_3/Relu</title></g><path class="node node-border" d="M5,0h26.390625a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-26.390625a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/fc3/Gemm" class="node graph-node" transform="translate(1418.3480834960938,0)" style=""><g class="node-item node-item-type node-item-type-layer" transform="translate(0,0)"><path d="M5,0h65a5,5 0 0 1 5,5v15a0,0 0 0 1 0,0h-75a0,0 0 0 1 0,0v-15a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(51, 85, 136); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Gemm</text><title>/fc3/Gemm</title></g><g class="node-attribute-list" transform="translate(0,20)"><path d="M0,0h75a0,0 0 0 1 0,0v25a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-25a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="12" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[9,84]</title><tspan style="font-weight: bold;">B</tspan><tspan>〈9×84〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="24" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[9]</title><tspan style="font-weight: bold;">C</tspan><tspan>〈9〉</tspan></text></g><line class="node" x1="0" x2="75" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h65a5,5 0 0 1 5,5v40a5,5 0 0 1 -5,5h-65a5,5 0 0 1 -5,-5v-40a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-/softmax/Softmax" class="node graph-node" transform="translate(1530.6280822753906,15)" style=""><g class="node-item node-item-type node-item-type-activation" transform="translate(0,0)"><path d="M5,0h46.22273254394531a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-46.22273254394531a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(112, 41, 33); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">Softmax</text><title>/softmax/Softmax</title></g><path class="node node-border" d="M5,0h46.22273254394531a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-46.22273254394531a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g class="node" transform="translate(1624.1308135986328,15)" style=""><g class="node-item graph-item-output" transform="translate(0,0)"><path d="M5,0h16.421875a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-16.421875a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(238, 238, 238); stroke-width: 0;"/><text x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none;">23</text><title>float32[1,9]</title></g><path class="node node-border" d="M5,0h16.421875a5,5 0 0 1 5,5v10a5,5 0 0 1 -5,5h-16.421875a5,5 0 0 1 -5,-5v-10a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g></g></g></svg>