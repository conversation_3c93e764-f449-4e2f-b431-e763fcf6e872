<?xml version="1.0"?>
<?xml-model
   href="http://download.ros.org/schema/package_format3.xsd"
   schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>armor_solver</name>
  <version>0.1.0</version>
  <description>A template for ROS packages.</description>
  <maintainer email="<EMAIL>"><PERSON> Jun</maintainer>
  <license>BSD</license>
  <url type="website">https://github.com/chenjunnn/rm_auto_aim</url>
  <url type="bugtracker">https://github.com/chenjunnn/rm_auto_aim/issues</url>
  <author email="<EMAIL>">Chen Jun</author>

  <!-- buildtool_depend: dependencies of the build process -->
  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- depend: build, export, and execution dependency -->
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>eigen</depend>
  <depend>angles</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>message_filters</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>rm_interfaces</depend>
  <depend>rm_utils</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_clang_format</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
