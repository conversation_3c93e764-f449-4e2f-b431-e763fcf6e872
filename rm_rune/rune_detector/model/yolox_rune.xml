<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="images" type="Parameter" version="opset1">
			<data shape="1,3,480,480" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="images">
					<dim>1</dim>
					<dim>3</dim>
					<dim>480</dim>
					<dim>480</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="onnx::Conv_925_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 3, 4, 4" offset="0" size="3840" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>3</dim>
					<dim>4</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="onnx::Conv_925" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>3</dim>
					<dim>4</dim>
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_925">
					<dim>40</dim>
					<dim>3</dim>
					<dim>4</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="/backbone/embedding/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="4, 4" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>480</dim>
					<dim>480</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>3</dim>
					<dim>4</dim>
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="Reshape_212_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 40, 1, 1" offset="3840" size="80" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="Reshape_212" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="/backbone/embedding/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/embedding/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="/backbone/embedding/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/embedding/act/Relu_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Constant_217" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="9" name="Constant_218" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="3928" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/backbone/stage1/block0/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage1/block0/conv1/Split_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage1/block0/conv1/Split_output_1">
					<dim>1</dim>
					<dim>30</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="backbone.stage1.block0.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 10, 3, 3" offset="3944" size="1800" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>10</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="backbone.stage1.block0.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>10</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage1.block0.conv1.conv.weight">
					<dim>10</dim>
					<dim>10</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="/backbone/stage1/block0/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>10</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage1/block0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="/backbone/stage1/block0/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>30</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage1/block0/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="onnx::Conv_928_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 40, 1, 1" offset="5744" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="onnx::Conv_928" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_928">
					<dim>80</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="/backbone/stage1/block0/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>40</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Reshape_235_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 80, 1, 1" offset="12144" size="160" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="Reshape_235" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/backbone/stage1/block0/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage1/block0/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="/backbone/stage1/block0/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage1/block0/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="backbone.stage1.block0.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 80, 1, 1" offset="12304" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="backbone.stage1.block0.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage1.block0.conv3.weight">
					<dim>40</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="/backbone/stage1/block0/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage1/block0/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="/backbone/stage1/block0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage1/block0/Add_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="onnx::Conv_931_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 40, 2, 2" offset="18704" size="25600" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>40</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="onnx::Conv_931" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>40</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_931">
					<dim>80</dim>
					<dim>40</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="/backbone/merging1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>40</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="Reshape_259_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 80, 1, 1" offset="44304" size="160" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="Reshape_259" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="/backbone/merging1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/merging1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/backbone/merging1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/merging1/act/Relu_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="Range_270" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="44464" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/backbone/se1/globalavgpool/GlobalAveragePool" type="ReduceMean" version="opset1">
			<data keep_dims="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/globalavgpool/GlobalAveragePool_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="/backbone/se1/Constant_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="44480" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se1/Constant_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="/backbone/se1/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/Reshape_output_0">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="backbone.se1.fc.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="5, 80" offset="44496" size="800" />
			<output>
				<port id="0" precision="FP16">
					<dim>5</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="backbone.se1.fc.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>5</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se1.fc.0.weight">
					<dim>5</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="/backbone/se1/fc/fc.0/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>5</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="Constant_7513_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 5" offset="45296" size="10" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="Constant_7513" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="/backbone/se1/fc/fc.0/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/fc/fc.0/Gemm_output_0">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="/backbone/se1/fc/fc.1/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se1/fc/fc.1/Relu_output_0">
					<dim>1</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="backbone.se1.fc.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 5" offset="45306" size="800" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="backbone.se1.fc.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se1.fc.2.weight">
					<dim>80</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="/backbone/se1/fc/fc.2/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>5</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="Constant_7514_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 80" offset="46106" size="160" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="Constant_7514" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="/backbone/se1/fc/fc.2/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/fc/fc.2/Gemm_output_0">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="/backbone/se1/fc/fc.3/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se1/fc/fc.3/Sigmoid_output_0">
					<dim>1</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="/backbone/se1/Constant_1_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="46266" size="32" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se1/Constant_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="/backbone/se1/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/Reshape_1_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="/backbone/se1/Mul" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se1/Mul_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="Constant_293" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="55" name="Constant_294" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="46298" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/backbone/stage2/block0/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage2/block0/conv1/Split_output_0">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage2/block0/conv1/Split_output_1">
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="backbone.stage2.block0.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="20, 20, 3, 3" offset="46314" size="7200" />
			<output>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="backbone.stage2.block0.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage2.block0.conv1.conv.weight">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="/backbone/stage2/block0/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="/backbone/stage2/block0/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block0/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="onnx::Conv_934_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 80, 1, 1" offset="53514" size="25600" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="onnx::Conv_934" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_934">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="/backbone/stage2/block0/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="Reshape_311_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 160, 1, 1" offset="79114" size="320" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="Reshape_311" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="/backbone/stage2/block0/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block0/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="/backbone/stage2/block0/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage2/block0/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="backbone.stage2.block0.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 160, 1, 1" offset="79434" size="25600" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="backbone.stage2.block0.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage2.block0.conv3.weight">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="/backbone/stage2/block0/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block0/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="/backbone/stage2/block0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block0/Add_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="Constant_323" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="73" name="Constant_324" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="46298" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="/backbone/stage2/block1/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage2/block1/conv1/Split_output_0">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage2/block1/conv1/Split_output_1">
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="backbone.stage2.block1.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="20, 20, 3, 3" offset="105034" size="7200" />
			<output>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="backbone.stage2.block1.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage2.block1.conv1.conv.weight">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="/backbone/stage2/block1/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>20</dim>
					<dim>20</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="/backbone/stage2/block1/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block1/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="onnx::Conv_937_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 80, 1, 1" offset="112234" size="25600" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="onnx::Conv_937" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_937">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="/backbone/stage2/block1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="Reshape_341_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 160, 1, 1" offset="137834" size="320" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="Reshape_341" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="/backbone/stage2/block1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="/backbone/stage2/block1/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage2/block1/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="backbone.stage2.block1.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 160, 1, 1" offset="138154" size="25600" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="backbone.stage2.block1.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage2.block1.conv3.weight">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="/backbone/stage2/block1/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block1/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="/backbone/stage2/block1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage2/block1/Add_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="onnx::Conv_940_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 80, 2, 2" offset="163754" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="onnx::Conv_940" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>80</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_940">
					<dim>160</dim>
					<dim>80</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="/backbone/merging2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>80</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="Reshape_365_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 160, 1, 1" offset="266154" size="320" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="Reshape_365" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="/backbone/merging2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/merging2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="/backbone/merging2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/merging2/act/Relu_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="Range_376" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="44464" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="/backbone/se2/globalavgpool/GlobalAveragePool" type="ReduceMean" version="opset1">
			<data keep_dims="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/globalavgpool/GlobalAveragePool_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="/backbone/se2/Constant_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="266474" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se2/Constant_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="/backbone/se2/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/Reshape_output_0">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="backbone.se2.fc.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 160" offset="266490" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="backbone.se2.fc.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se2.fc.0.weight">
					<dim>10</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/backbone/se2/fc/fc.0/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="Constant_7515_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10" offset="269690" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="Constant_7515" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="/backbone/se2/fc/fc.0/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/fc/fc.0/Gemm_output_0">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="/backbone/se2/fc/fc.1/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se2/fc/fc.1/Relu_output_0">
					<dim>1</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="backbone.se2.fc.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 10" offset="269710" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="backbone.se2.fc.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se2.fc.2.weight">
					<dim>160</dim>
					<dim>10</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="/backbone/se2/fc/fc.2/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>10</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="Constant_7516_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 160" offset="272910" size="320" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="Constant_7516" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="/backbone/se2/fc/fc.2/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/fc/fc.2/Gemm_output_0">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/backbone/se2/fc/fc.3/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se2/fc/fc.3/Sigmoid_output_0">
					<dim>1</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="/backbone/se2/Constant_1_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="273230" size="32" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se2/Constant_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="/backbone/se2/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/Reshape_1_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="/backbone/se2/Mul" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se2/Mul_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="Constant_399" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="119" name="Constant_400" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="/backbone/stage3/block0/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block0/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block0/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="backbone.stage3.block0.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="273278" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="backbone.stage3.block0.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block0.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="/backbone/stage3/block0/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="/backbone/stage3/block0/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block0/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="onnx::Conv_943_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="302078" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="onnx::Conv_943" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_943">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="/backbone/stage3/block0/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="Reshape_417_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="404478" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="Reshape_417" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="/backbone/stage3/block0/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block0/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="/backbone/stage3/block0/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block0/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="backbone.stage3.block0.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="405118" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="backbone.stage3.block0.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block0.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="/backbone/stage3/block0/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block0/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="/backbone/stage3/block0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block0/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="Constant_429" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="137" name="Constant_430" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="/backbone/stage3/block1/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block1/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block1/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="backbone.stage3.block1.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="507518" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="backbone.stage3.block1.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block1.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="/backbone/stage3/block1/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="/backbone/stage3/block1/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block1/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="onnx::Conv_946_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="536318" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="onnx::Conv_946" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_946">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="/backbone/stage3/block1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="Reshape_447_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="638718" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="Reshape_447" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="/backbone/stage3/block1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="/backbone/stage3/block1/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block1/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="backbone.stage3.block1.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="639358" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="backbone.stage3.block1.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block1.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="/backbone/stage3/block1/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block1/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="/backbone/stage3/block1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block1/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="Constant_459" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="155" name="Constant_460" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="/backbone/stage3/block2/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block2/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block2/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="backbone.stage3.block2.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="741758" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="backbone.stage3.block2.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block2.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="/backbone/stage3/block2/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block2/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="/backbone/stage3/block2/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block2/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="onnx::Conv_949_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="770558" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="onnx::Conv_949" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_949">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="/backbone/stage3/block2/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="Reshape_477_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="872958" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="Reshape_477" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="/backbone/stage3/block2/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block2/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="/backbone/stage3/block2/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block2/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="backbone.stage3.block2.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="873598" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="backbone.stage3.block2.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block2.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="/backbone/stage3/block2/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block2/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="/backbone/stage3/block2/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block2/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="Constant_489" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="173" name="Constant_490" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="/backbone/stage3/block3/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block3/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block3/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="backbone.stage3.block3.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="975998" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="backbone.stage3.block3.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block3.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="/backbone/stage3/block3/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block3/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="/backbone/stage3/block3/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block3/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="onnx::Conv_952_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="1004798" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="onnx::Conv_952" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_952">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="/backbone/stage3/block3/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="Reshape_507_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="1107198" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="Reshape_507" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="/backbone/stage3/block3/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block3/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="/backbone/stage3/block3/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block3/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="backbone.stage3.block3.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="1107838" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="backbone.stage3.block3.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block3.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="/backbone/stage3/block3/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block3/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="/backbone/stage3/block3/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block3/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="Constant_519" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="191" name="Constant_520" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="/backbone/stage3/block4/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block4/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block4/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="backbone.stage3.block4.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="1210238" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="backbone.stage3.block4.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block4.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="/backbone/stage3/block4/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block4/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="/backbone/stage3/block4/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block4/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="onnx::Conv_955_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="1239038" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="onnx::Conv_955" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_955">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="/backbone/stage3/block4/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="Reshape_537_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="1341438" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="Reshape_537" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="/backbone/stage3/block4/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block4/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="/backbone/stage3/block4/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block4/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="backbone.stage3.block4.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="1342078" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="backbone.stage3.block4.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block4.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="/backbone/stage3/block4/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block4/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="/backbone/stage3/block4/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block4/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="Constant_549" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="209" name="Constant_550" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="/backbone/stage3/block5/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block5/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block5/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="backbone.stage3.block5.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="1444478" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="backbone.stage3.block5.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block5.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="/backbone/stage3/block5/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block5/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="/backbone/stage3/block5/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block5/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="onnx::Conv_958_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="1473278" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="onnx::Conv_958" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_958">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="/backbone/stage3/block5/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="Reshape_567_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="1575678" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="Reshape_567" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="/backbone/stage3/block5/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block5/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="/backbone/stage3/block5/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block5/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="backbone.stage3.block5.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="1576318" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="backbone.stage3.block5.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block5.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="/backbone/stage3/block5/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block5/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="/backbone/stage3/block5/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block5/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="Constant_579" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="227" name="Constant_580" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="/backbone/stage3/block6/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block6/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block6/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="backbone.stage3.block6.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="1678718" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="backbone.stage3.block6.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block6.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="/backbone/stage3/block6/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block6/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="/backbone/stage3/block6/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block6/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="onnx::Conv_961_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="1707518" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="onnx::Conv_961" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_961">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="/backbone/stage3/block6/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="Reshape_597_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="1809918" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="Reshape_597" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="/backbone/stage3/block6/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block6/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="/backbone/stage3/block6/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block6/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="backbone.stage3.block6.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="1810558" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="backbone.stage3.block6.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block6.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="/backbone/stage3/block6/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block6/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="/backbone/stage3/block6/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block6/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="Constant_609" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="245" name="Constant_610" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="273262" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="/backbone/stage3/block7/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage3/block7/conv1/Split_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage3/block7/conv1/Split_output_1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="backbone.stage3.block7.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="40, 40, 3, 3" offset="1912958" size="28800" />
			<output>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="backbone.stage3.block7.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block7.conv1.conv.weight">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="/backbone/stage3/block7/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>40</dim>
					<dim>40</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block7/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="/backbone/stage3/block7/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>40</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>120</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block7/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="onnx::Conv_964_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 1, 1" offset="1941758" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="onnx::Conv_964" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_964">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="/backbone/stage3/block7/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="Reshape_627_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="2044158" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="Reshape_627" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="/backbone/stage3/block7/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block7/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="/backbone/stage3/block7/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage3/block7/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="backbone.stage3.block7.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="160, 320, 1, 1" offset="2044798" size="102400" />
			<output>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="backbone.stage3.block7.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage3.block7.conv3.weight">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="/backbone/stage3/block7/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>160</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block7/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="/backbone/stage3/block7/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage3/block7/Add_output_0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="onnx::Conv_967_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 160, 2, 2" offset="2147198" size="409600" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="onnx::Conv_967" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>160</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_967">
					<dim>320</dim>
					<dim>160</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="/backbone/merging3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>160</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="Reshape_651_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320, 1, 1" offset="2556798" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="Reshape_651" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="/backbone/merging3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/merging3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="/backbone/merging3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/merging3/act/Relu_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="Range_662" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="44464" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="/backbone/se3/globalavgpool/GlobalAveragePool" type="ReduceMean" version="opset1">
			<data keep_dims="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/globalavgpool/GlobalAveragePool_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="/backbone/se3/Constant_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="2557438" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se3/Constant_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="/backbone/se3/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/Reshape_output_0">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="backbone.se3.fc.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="20, 320" offset="2557454" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="backbone.se3.fc.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>20</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se3.fc.0.weight">
					<dim>20</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="/backbone/se3/fc/fc.0/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>20</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="Constant_7517_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 20" offset="2570254" size="40" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="Constant_7517" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="/backbone/se3/fc/fc.0/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/fc/fc.0/Gemm_output_0">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="/backbone/se3/fc/fc.1/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se3/fc/fc.1/Relu_output_0">
					<dim>1</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="backbone.se3.fc.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 20" offset="2570294" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="backbone.se3.fc.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.se3.fc.2.weight">
					<dim>320</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="/backbone/se3/fc/fc.2/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="Constant_7518_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 320" offset="2583094" size="640" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="Constant_7518" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="/backbone/se3/fc/fc.2/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/fc/fc.2/Gemm_output_0">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="/backbone/se3/fc/fc.3/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/se3/fc/fc.3/Sigmoid_output_0">
					<dim>1</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="/backbone/se3/Constant_1_output_0" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="2583734" size="32" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="/backbone/se3/Constant_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="/backbone/se3/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/Reshape_1_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="/backbone/se3/Mul" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/se3/Mul_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="Constant_685" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="291" name="Constant_686" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="2583766" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="/backbone/stage4/block0/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage4/block0/conv1/Split_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage4/block0/conv1/Split_output_1">
					<dim>1</dim>
					<dim>240</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="backbone.stage4.block0.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 80, 3, 3" offset="2583782" size="115200" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="backbone.stage4.block0.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage4.block0.conv1.conv.weight">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="/backbone/stage4/block0/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="/backbone/stage4/block0/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>240</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block0/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="onnx::Conv_970_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="640, 320, 1, 1" offset="2698982" size="409600" />
			<output>
				<port id="0" precision="FP16">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="onnx::Conv_970" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_970">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="/backbone/stage4/block0/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="Reshape_703_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 640, 1, 1" offset="3108582" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="Reshape_703" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="/backbone/stage4/block0/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block0/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="/backbone/stage4/block0/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage4/block0/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="backbone.stage4.block0.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 640, 1, 1" offset="3109862" size="409600" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="backbone.stage4.block0.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage4.block0.conv3.weight">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="/backbone/stage4/block0/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block0/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="/backbone/stage4/block0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block0/Add_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="Constant_715" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="3920" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="309" name="Constant_716" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="2583766" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="/backbone/stage4/block1/conv1/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/backbone/stage4/block1/conv1/Split_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="4" precision="FP32" names="/backbone/stage4/block1/conv1/Split_output_1">
					<dim>1</dim>
					<dim>240</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="backbone.stage4.block1.conv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="80, 80, 3, 3" offset="3519462" size="115200" />
			<output>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="backbone.stage4.block1.conv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage4.block1.conv1.conv.weight">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="/backbone/stage4/block1/conv1/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>80</dim>
					<dim>80</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="/backbone/stage4/block1/conv1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>240</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block1/conv1/Concat_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="onnx::Conv_973_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="640, 320, 1, 1" offset="3634662" size="409600" />
			<output>
				<port id="0" precision="FP16">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="onnx::Conv_973" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_973">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="/backbone/stage4/block1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>640</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="Reshape_733_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 640, 1, 1" offset="4044262" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="Reshape_733" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="/backbone/stage4/block1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="/backbone/stage4/block1/conv2/act/Mul_1" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stage4/block1/conv2/act/Mul_1_output_0">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="backbone.stage4.block1.conv3.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="320, 640, 1, 1" offset="4045542" size="409600" />
			<output>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="backbone.stage4.block1.conv3.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="backbone.stage4.block1.conv3.weight">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="/backbone/stage4/block1/conv3/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>640</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>320</dim>
					<dim>640</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block1/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="/backbone/stage4/block1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stage4/block1/Add_output_0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="onnx::Conv_982_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 320, 1, 1" offset="4455142" size="81920" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="onnx::Conv_982" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_982">
					<dim>128</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="/neck/reduce_layers.2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>320</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="Reshape_791_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4537062" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="Reshape_791" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="/neck/reduce_layers.2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/reduce_layers.2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="/neck/reduce_layers.2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/reduce_layers.2/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="/neck/upsample/Constant_output_0" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="4537318" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="/neck/upsample/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="/neck/upsample/Resize" type="Interpolate" version="opset11">
			<data mode="linear_onnx" shape_calculation_mode="scales" coordinate_transformation_mode="half_pixel" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/upsample/Resize_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="onnx::Conv_979_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 160, 1, 1" offset="4537334" size="40960" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="onnx::Conv_979" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_979">
					<dim>128</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="/neck/reduce_layers.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>160</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>160</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="Reshape_774_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4578294" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="Reshape_774" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="/neck/reduce_layers.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/reduce_layers.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="/neck/reduce_layers.1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/reduce_layers.1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="/neck/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="onnx::Conv_985_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="4578550" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="onnx::Conv_985" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_985">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="Reshape_810_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4611318" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="Reshape_810" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="Reshape_822_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="4611446" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="Reshape_822" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="Reshape_874_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4612598" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="Reshape_874" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost1/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="onnx::Conv_991_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="4612726" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="onnx::Conv_991" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_991">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="Reshape_892_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4629110" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="Reshape_892" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="Reshape_903_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="4629238" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="Reshape_903" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="Reshape_955_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4630390" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="Reshape_955" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/ghost2/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="Reshape_967_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 1, 1, 5, 5" offset="4630518" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="Reshape_967" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="Reshape_1019_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="4643318" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="Reshape_1019" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="onnx::Conv_1000_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="4643830" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="onnx::Conv_1000" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1000">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="Reshape_1035_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4709366" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="Reshape_1035" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="/neck/top_down_blocks.0/blocks/blocks.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.0/blocks/blocks.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="/neck/upsample_1/Resize" type="Interpolate" version="opset11">
			<data mode="linear_onnx" shape_calculation_mode="scales" coordinate_transformation_mode="half_pixel" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/upsample_1/Resize_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="onnx::Conv_976_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 80, 1, 1" offset="4709622" size="20480" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="onnx::Conv_976" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_976">
					<dim>128</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="/neck/reduce_layers.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>80</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>80</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="Reshape_757_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4730102" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="Reshape_757" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="/neck/reduce_layers.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/reduce_layers.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="/neck/reduce_layers.0/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/reduce_layers.0/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="/neck/Concat_1" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_1_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="onnx::Conv_1003_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="4730358" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="onnx::Conv_1003" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1003">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="Reshape_1054_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4763126" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="Reshape_1054" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="Reshape_1066_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="4763254" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="Reshape_1066" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="Reshape_1118_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4764406" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="Reshape_1118" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="407" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost1/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="408" name="onnx::Conv_1009_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="4764534" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="409" name="onnx::Conv_1009" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1009">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="410" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="Reshape_1136_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4780918" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="Reshape_1136" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="Reshape_1147_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="4781046" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="Reshape_1147" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="416" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="417" name="Reshape_1199_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4782198" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="418" name="Reshape_1199" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/ghost2/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="Reshape_1211_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 1, 1, 5, 5" offset="4782326" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="Reshape_1211" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="Reshape_1263_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="4795126" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="Reshape_1263" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="427" name="onnx::Conv_1018_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="4795638" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="428" name="onnx::Conv_1018" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1018">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="Reshape_1279_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4861174" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="Reshape_1279" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="/neck/top_down_blocks.1/blocks/blocks.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/top_down_blocks.1/blocks/blocks.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="onnx::Conv_1069_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="4861430" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="onnx::Conv_1069" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1069">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="/head/stems.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="Reshape_1942_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4877814" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="Reshape_1942" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="/head/stems.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="/head/stems.0/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.0/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="Reshape_1954_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="4877942" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="Reshape_1954" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="Reshape_2006_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4881142" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="Reshape_2006" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="Reshape_2017_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="4881270" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="Reshape_2017" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="Reshape_2069_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4889462" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="Reshape_2069" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="/head/group_convs.0/group_convs.0.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.0/group_convs.0.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="Reshape_2081_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="4889718" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="Reshape_2081" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="Reshape_2133_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4896118" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="Reshape_2133" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="Reshape_2144_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="4896374" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="Reshape_2144" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="Reshape_2196_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4912758" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="Reshape_2196" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="/head/group_convs.0/group_convs.0.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.0/group_convs.0.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="Constant_7098" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913014" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="Constant_7101" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="Constant_7104" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="/head/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="head.reg_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="4913062" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="head.reg_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.0.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="/head/reg_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="Reshape_2231_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="4914342" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="Reshape_2231" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="/head/reg_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="477" name="head.obj_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4914362" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="head.obj_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.0.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="/head/obj_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="Reshape_2247_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="4914490" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="481" name="Reshape_2247" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="482" name="/head/obj_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="483" name="/head/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="484" name="Constant_7110" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="485" name="Constant_7113" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4914492" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="486" name="Constant_7116" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="487" name="/head/Slice_1" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_1_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="488" name="head.cls_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="4914508" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="head.cls_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.0.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="/head/cls_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="491" name="Reshape_2215_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="4915020" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="492" name="Reshape_2215" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="493" name="/head/cls_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="494" name="/head/Sigmoid_1" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_1_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="495" name="/head/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="496" name="520" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4915028" size="24" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="520">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="497" name="/head/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>3600</dim>
				</port>
			</output>
		</layer>
		<layer id="498" name="Reshape_1291_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="4915052" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="Reshape_1291" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="/neck/downsamples.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="Reshape_1343_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4921452" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="Reshape_1343" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="/neck/downsamples.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/downsamples.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="onnx::Conv_1024_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="4921708" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="onnx::Conv_1024" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1024">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="506" name="/neck/downsamples.0/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="507" name="Reshape_1359_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4954476" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="508" name="Reshape_1359" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="509" name="/neck/downsamples.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/downsamples.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="510" name="/neck/downsamples.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/downsamples.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="511" name="/neck/Concat_2" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_2_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="512" name="onnx::Conv_1027_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="4954732" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="513" name="onnx::Conv_1027" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1027">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="Reshape_1377_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4987500" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="Reshape_1377" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="Reshape_1389_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="4987628" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="Reshape_1389" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="Reshape_1441_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4988780" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="Reshape_1441" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost1/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="527" name="onnx::Conv_1033_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="4988908" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="528" name="onnx::Conv_1033" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1033">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="529" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="530" name="Reshape_1459_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5005292" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="531" name="Reshape_1459" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="532" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="533" name="Reshape_1470_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="5005420" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="534" name="Reshape_1470" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="Reshape_1522_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5006572" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="Reshape_1522" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/ghost2/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="Reshape_1534_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 1, 1, 5, 5" offset="5006700" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="541" name="Reshape_1534" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="542" name="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="543" name="Reshape_1586_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="5019500" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="544" name="Reshape_1586" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="545" name="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.0/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="546" name="onnx::Conv_1042_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="5020012" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="547" name="onnx::Conv_1042" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1042">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="548" name="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="549" name="Reshape_1602_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5085548" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="550" name="Reshape_1602" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="551" name="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/shortcut/shortcut.2/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="552" name="/neck/bottom_up_blocks.0/blocks/blocks.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.0/blocks/blocks.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="553" name="onnx::Conv_1084_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="5085804" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="554" name="onnx::Conv_1084" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1084">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="555" name="/head/stems.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="556" name="Reshape_2266_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5102188" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="557" name="Reshape_2266" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="558" name="/head/stems.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="559" name="/head/stems.1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="560" name="Reshape_2278_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="5102316" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="561" name="Reshape_2278" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="562" name="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="563" name="Reshape_2330_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5105516" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="564" name="Reshape_2330" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="565" name="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="566" name="Reshape_2341_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="5105644" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="567" name="Reshape_2341" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="568" name="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="569" name="Reshape_2393_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5113836" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="570" name="Reshape_2393" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="571" name="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="572" name="/head/group_convs.1/group_convs.1.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.1/group_convs.1.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="573" name="Reshape_2405_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="5114092" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="574" name="Reshape_2405" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="575" name="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="576" name="Reshape_2457_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5120492" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="577" name="Reshape_2457" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="578" name="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="579" name="Reshape_2468_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="5120748" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="580" name="Reshape_2468" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="581" name="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="582" name="Reshape_2520_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5137132" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="583" name="Reshape_2520" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="584" name="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="585" name="/head/group_convs.1/group_convs.1.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.1/group_convs.1.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="586" name="Constant_7122" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913014" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="587" name="Constant_7125" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="588" name="Constant_7128" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="589" name="/head/Slice_2" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_2_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="590" name="head.reg_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="5137388" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="591" name="head.reg_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.1.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="592" name="/head/reg_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="593" name="Reshape_2555_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="5138668" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="594" name="Reshape_2555" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="595" name="/head/reg_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="596" name="head.obj_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5138688" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="597" name="head.obj_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.1.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="598" name="/head/obj_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="599" name="Reshape_2571_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="5138816" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="600" name="Reshape_2571" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="601" name="/head/obj_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="602" name="/head/Sigmoid_2" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_2_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="603" name="Constant_7134" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="604" name="Constant_7137" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4914492" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="605" name="Constant_7140" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="606" name="/head/Slice_3" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_3_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="607" name="head.cls_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="5138818" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="608" name="head.cls_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.1.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="609" name="/head/cls_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="610" name="Reshape_2539_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="5139330" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="611" name="Reshape_2539" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="612" name="/head/cls_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="613" name="/head/Sigmoid_3" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_3_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="614" name="/head/Concat_1" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_1_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="615" name="/head/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_1_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>900</dim>
				</port>
			</output>
		</layer>
		<layer id="616" name="Reshape_1614_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="5139338" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="617" name="Reshape_1614" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="618" name="/neck/downsamples.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="619" name="Reshape_1666_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5145738" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="620" name="Reshape_1666" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="621" name="/neck/downsamples.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/downsamples.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="622" name="onnx::Conv_1048_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="5145994" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="623" name="onnx::Conv_1048" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1048">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="624" name="/neck/downsamples.1/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="625" name="Reshape_1682_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5178762" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="626" name="Reshape_1682" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="627" name="/neck/downsamples.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/downsamples.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="628" name="/neck/downsamples.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/downsamples.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="629" name="/neck/Concat_3" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_3_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="630" name="onnx::Conv_1051_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="5179018" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="631" name="onnx::Conv_1051" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1051">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="632" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="633" name="Reshape_1700_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5211786" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="634" name="Reshape_1700" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="635" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="636" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/primary_conv/primary_conv.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="637" name="Reshape_1712_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="5211914" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="638" name="Reshape_1712" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="639" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="640" name="Reshape_1764_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5213066" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="641" name="Reshape_1764" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="642" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="643" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/cheap_operation/cheap_operation.2/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="644" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost1/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="645" name="onnx::Conv_1057_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="5213194" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="646" name="onnx::Conv_1057" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1057">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="647" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="648" name="Reshape_1782_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5229578" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="649" name="Reshape_1782" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="650" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/primary_conv/primary_conv.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="651" name="Reshape_1793_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="5229706" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="652" name="Reshape_1793" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="653" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="654" name="Reshape_1845_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5230858" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="655" name="Reshape_1845" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="656" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/cheap_operation/cheap_operation.0/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="657" name="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/ghost2/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="658" name="Reshape_1857_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 1, 1, 5, 5" offset="5230986" size="12800" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="659" name="Reshape_1857" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="660" name="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="661" name="Reshape_1909_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="5243786" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="662" name="Reshape_1909" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="663" name="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.0/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="664" name="onnx::Conv_1066_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="5244298" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="665" name="onnx::Conv_1066" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1066">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="666" name="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="667" name="Reshape_1925_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5309834" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="668" name="Reshape_1925" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="669" name="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/shortcut/shortcut.2/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="670" name="/neck/bottom_up_blocks.1/blocks/blocks.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bottom_up_blocks.1/blocks/blocks.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="671" name="onnx::Conv_1099_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="5310090" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="672" name="onnx::Conv_1099" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1099">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="673" name="/head/stems.2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="674" name="Reshape_2590_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5326474" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="675" name="Reshape_2590" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="676" name="/head/stems.2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="677" name="/head/stems.2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.2/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="678" name="Reshape_2602_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="5326602" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="679" name="Reshape_2602" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="680" name="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="681" name="Reshape_2654_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5329802" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="682" name="Reshape_2654" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="683" name="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="684" name="Reshape_2665_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="5329930" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="685" name="Reshape_2665" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="686" name="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="687" name="Reshape_2717_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5338122" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="688" name="Reshape_2717" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="689" name="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="690" name="/head/group_convs.2/group_convs.2.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.2/group_convs.2.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="691" name="Reshape_2729_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="5338378" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="692" name="Reshape_2729" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="693" name="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="694" name="Reshape_2781_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5344778" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="695" name="Reshape_2781" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="696" name="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="697" name="Reshape_2792_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="5345034" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="698" name="Reshape_2792" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="699" name="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="700" name="Reshape_2844_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5361418" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="701" name="Reshape_2844" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="702" name="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="703" name="/head/group_convs.2/group_convs.2.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.2/group_convs.2.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="704" name="Constant_7146" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913014" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="705" name="Constant_7149" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="706" name="Constant_7152" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="707" name="/head/Slice_4" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_4_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="708" name="head.reg_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="5361674" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="709" name="head.reg_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.2.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="710" name="/head/reg_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="711" name="Reshape_2879_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="5362954" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="712" name="Reshape_2879" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="713" name="/head/reg_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="714" name="head.obj_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5362974" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="715" name="head.obj_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.2.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="716" name="/head/obj_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="717" name="Reshape_2895_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="5363102" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="718" name="Reshape_2895" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="719" name="/head/obj_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="720" name="/head/Sigmoid_4" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_4_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="721" name="Constant_7158" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913030" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="722" name="Constant_7161" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4914492" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="723" name="Constant_7164" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4913046" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="724" name="/head/Slice_5" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_5_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="725" name="head.cls_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="5363104" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="726" name="head.cls_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.2.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="727" name="/head/cls_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="728" name="Reshape_2863_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="5363616" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="729" name="Reshape_2863" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="730" name="/head/cls_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="731" name="/head/Sigmoid_5" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_5_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="732" name="/head/Concat_2" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_2_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="733" name="/head/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_2_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>225</dim>
				</port>
			</output>
		</layer>
		<layer id="734" name="/head/Concat_6" type="Concat" version="opset1">
			<data axis="2" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>3600</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>900</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>225</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_6_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>4725</dim>
				</port>
			</output>
		</layer>
		<layer id="735" name="Constant_2906" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="5363624" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="736" name="output" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>4725</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="output">
					<dim>1</dim>
					<dim>4725</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="737" name="output/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4725</dim>
					<dim>15</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="3" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="2" from-port="1" to-layer="3" to-port="1" />
		<edge from-layer="3" from-port="2" to-layer="6" to-port="0" />
		<edge from-layer="4" from-port="0" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="6" to-port="1" />
		<edge from-layer="6" from-port="2" to-layer="7" to-port="0" />
		<edge from-layer="7" from-port="1" to-layer="10" to-port="0" />
		<edge from-layer="7" from-port="1" to-layer="25" to-port="0" />
		<edge from-layer="8" from-port="0" to-layer="10" to-port="1" />
		<edge from-layer="9" from-port="0" to-layer="10" to-port="2" />
		<edge from-layer="10" from-port="4" to-layer="14" to-port="1" />
		<edge from-layer="10" from-port="3" to-layer="13" to-port="0" />
		<edge from-layer="11" from-port="0" to-layer="12" to-port="0" />
		<edge from-layer="12" from-port="1" to-layer="13" to-port="1" />
		<edge from-layer="13" from-port="2" to-layer="14" to-port="0" />
		<edge from-layer="14" from-port="2" to-layer="17" to-port="0" />
		<edge from-layer="15" from-port="0" to-layer="16" to-port="0" />
		<edge from-layer="16" from-port="1" to-layer="17" to-port="1" />
		<edge from-layer="17" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="18" from-port="0" to-layer="19" to-port="0" />
		<edge from-layer="19" from-port="1" to-layer="20" to-port="1" />
		<edge from-layer="20" from-port="2" to-layer="21" to-port="0" />
		<edge from-layer="21" from-port="1" to-layer="24" to-port="0" />
		<edge from-layer="22" from-port="0" to-layer="23" to-port="0" />
		<edge from-layer="23" from-port="1" to-layer="24" to-port="1" />
		<edge from-layer="24" from-port="2" to-layer="25" to-port="1" />
		<edge from-layer="25" from-port="2" to-layer="28" to-port="0" />
		<edge from-layer="26" from-port="0" to-layer="27" to-port="0" />
		<edge from-layer="27" from-port="1" to-layer="28" to-port="1" />
		<edge from-layer="28" from-port="2" to-layer="31" to-port="0" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="0" />
		<edge from-layer="30" from-port="1" to-layer="31" to-port="1" />
		<edge from-layer="31" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="53" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="34" to-port="0" />
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="36" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="39" to-port="0" />
		<edge from-layer="37" from-port="0" to-layer="38" to-port="0" />
		<edge from-layer="38" from-port="1" to-layer="39" to-port="1" />
		<edge from-layer="39" from-port="2" to-layer="42" to-port="0" />
		<edge from-layer="40" from-port="0" to-layer="41" to-port="0" />
		<edge from-layer="41" from-port="1" to-layer="42" to-port="1" />
		<edge from-layer="42" from-port="2" to-layer="43" to-port="0" />
		<edge from-layer="43" from-port="1" to-layer="46" to-port="0" />
		<edge from-layer="44" from-port="0" to-layer="45" to-port="0" />
		<edge from-layer="45" from-port="1" to-layer="46" to-port="1" />
		<edge from-layer="46" from-port="2" to-layer="49" to-port="0" />
		<edge from-layer="47" from-port="0" to-layer="48" to-port="0" />
		<edge from-layer="48" from-port="1" to-layer="49" to-port="1" />
		<edge from-layer="49" from-port="2" to-layer="50" to-port="0" />
		<edge from-layer="50" from-port="1" to-layer="52" to-port="0" />
		<edge from-layer="51" from-port="0" to-layer="52" to-port="1" />
		<edge from-layer="52" from-port="2" to-layer="53" to-port="1" />
		<edge from-layer="53" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="53" from-port="2" to-layer="71" to-port="0" />
		<edge from-layer="54" from-port="0" to-layer="56" to-port="1" />
		<edge from-layer="55" from-port="0" to-layer="56" to-port="2" />
		<edge from-layer="56" from-port="3" to-layer="59" to-port="0" />
		<edge from-layer="56" from-port="4" to-layer="60" to-port="1" />
		<edge from-layer="57" from-port="0" to-layer="58" to-port="0" />
		<edge from-layer="58" from-port="1" to-layer="59" to-port="1" />
		<edge from-layer="59" from-port="2" to-layer="60" to-port="0" />
		<edge from-layer="60" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="61" from-port="0" to-layer="62" to-port="0" />
		<edge from-layer="62" from-port="1" to-layer="63" to-port="1" />
		<edge from-layer="63" from-port="2" to-layer="66" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="65" to-port="0" />
		<edge from-layer="65" from-port="1" to-layer="66" to-port="1" />
		<edge from-layer="66" from-port="2" to-layer="67" to-port="0" />
		<edge from-layer="67" from-port="1" to-layer="70" to-port="0" />
		<edge from-layer="68" from-port="0" to-layer="69" to-port="0" />
		<edge from-layer="69" from-port="1" to-layer="70" to-port="1" />
		<edge from-layer="70" from-port="2" to-layer="71" to-port="1" />
		<edge from-layer="71" from-port="2" to-layer="74" to-port="0" />
		<edge from-layer="71" from-port="2" to-layer="89" to-port="0" />
		<edge from-layer="72" from-port="0" to-layer="74" to-port="1" />
		<edge from-layer="73" from-port="0" to-layer="74" to-port="2" />
		<edge from-layer="74" from-port="4" to-layer="78" to-port="1" />
		<edge from-layer="74" from-port="3" to-layer="77" to-port="0" />
		<edge from-layer="75" from-port="0" to-layer="76" to-port="0" />
		<edge from-layer="76" from-port="1" to-layer="77" to-port="1" />
		<edge from-layer="77" from-port="2" to-layer="78" to-port="0" />
		<edge from-layer="78" from-port="2" to-layer="81" to-port="0" />
		<edge from-layer="79" from-port="0" to-layer="80" to-port="0" />
		<edge from-layer="80" from-port="1" to-layer="81" to-port="1" />
		<edge from-layer="81" from-port="2" to-layer="84" to-port="0" />
		<edge from-layer="82" from-port="0" to-layer="83" to-port="0" />
		<edge from-layer="83" from-port="1" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="85" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="88" to-port="0" />
		<edge from-layer="86" from-port="0" to-layer="87" to-port="0" />
		<edge from-layer="87" from-port="1" to-layer="88" to-port="1" />
		<edge from-layer="88" from-port="2" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="2" to-layer="92" to-port="0" />
		<edge from-layer="89" from-port="2" to-layer="387" to-port="0" />
		<edge from-layer="90" from-port="0" to-layer="91" to-port="0" />
		<edge from-layer="91" from-port="1" to-layer="92" to-port="1" />
		<edge from-layer="92" from-port="2" to-layer="95" to-port="0" />
		<edge from-layer="93" from-port="0" to-layer="94" to-port="0" />
		<edge from-layer="94" from-port="1" to-layer="95" to-port="1" />
		<edge from-layer="95" from-port="2" to-layer="96" to-port="0" />
		<edge from-layer="96" from-port="1" to-layer="117" to-port="0" />
		<edge from-layer="96" from-port="1" to-layer="98" to-port="0" />
		<edge from-layer="97" from-port="0" to-layer="98" to-port="1" />
		<edge from-layer="98" from-port="2" to-layer="100" to-port="0" />
		<edge from-layer="99" from-port="0" to-layer="100" to-port="1" />
		<edge from-layer="100" from-port="2" to-layer="103" to-port="0" />
		<edge from-layer="101" from-port="0" to-layer="102" to-port="0" />
		<edge from-layer="102" from-port="1" to-layer="103" to-port="1" />
		<edge from-layer="103" from-port="2" to-layer="106" to-port="0" />
		<edge from-layer="104" from-port="0" to-layer="105" to-port="0" />
		<edge from-layer="105" from-port="1" to-layer="106" to-port="1" />
		<edge from-layer="106" from-port="2" to-layer="107" to-port="0" />
		<edge from-layer="107" from-port="1" to-layer="110" to-port="0" />
		<edge from-layer="108" from-port="0" to-layer="109" to-port="0" />
		<edge from-layer="109" from-port="1" to-layer="110" to-port="1" />
		<edge from-layer="110" from-port="2" to-layer="113" to-port="0" />
		<edge from-layer="111" from-port="0" to-layer="112" to-port="0" />
		<edge from-layer="112" from-port="1" to-layer="113" to-port="1" />
		<edge from-layer="113" from-port="2" to-layer="114" to-port="0" />
		<edge from-layer="114" from-port="1" to-layer="116" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="1" />
		<edge from-layer="116" from-port="2" to-layer="117" to-port="1" />
		<edge from-layer="117" from-port="2" to-layer="120" to-port="0" />
		<edge from-layer="117" from-port="2" to-layer="135" to-port="0" />
		<edge from-layer="118" from-port="0" to-layer="120" to-port="1" />
		<edge from-layer="119" from-port="0" to-layer="120" to-port="2" />
		<edge from-layer="120" from-port="3" to-layer="123" to-port="0" />
		<edge from-layer="120" from-port="4" to-layer="124" to-port="1" />
		<edge from-layer="121" from-port="0" to-layer="122" to-port="0" />
		<edge from-layer="122" from-port="1" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="124" to-port="0" />
		<edge from-layer="124" from-port="2" to-layer="127" to-port="0" />
		<edge from-layer="125" from-port="0" to-layer="126" to-port="0" />
		<edge from-layer="126" from-port="1" to-layer="127" to-port="1" />
		<edge from-layer="127" from-port="2" to-layer="130" to-port="0" />
		<edge from-layer="128" from-port="0" to-layer="129" to-port="0" />
		<edge from-layer="129" from-port="1" to-layer="130" to-port="1" />
		<edge from-layer="130" from-port="2" to-layer="131" to-port="0" />
		<edge from-layer="131" from-port="1" to-layer="134" to-port="0" />
		<edge from-layer="132" from-port="0" to-layer="133" to-port="0" />
		<edge from-layer="133" from-port="1" to-layer="134" to-port="1" />
		<edge from-layer="134" from-port="2" to-layer="135" to-port="1" />
		<edge from-layer="135" from-port="2" to-layer="153" to-port="0" />
		<edge from-layer="135" from-port="2" to-layer="138" to-port="0" />
		<edge from-layer="136" from-port="0" to-layer="138" to-port="1" />
		<edge from-layer="137" from-port="0" to-layer="138" to-port="2" />
		<edge from-layer="138" from-port="3" to-layer="141" to-port="0" />
		<edge from-layer="138" from-port="4" to-layer="142" to-port="1" />
		<edge from-layer="139" from-port="0" to-layer="140" to-port="0" />
		<edge from-layer="140" from-port="1" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="142" to-port="0" />
		<edge from-layer="142" from-port="2" to-layer="145" to-port="0" />
		<edge from-layer="143" from-port="0" to-layer="144" to-port="0" />
		<edge from-layer="144" from-port="1" to-layer="145" to-port="1" />
		<edge from-layer="145" from-port="2" to-layer="148" to-port="0" />
		<edge from-layer="146" from-port="0" to-layer="147" to-port="0" />
		<edge from-layer="147" from-port="1" to-layer="148" to-port="1" />
		<edge from-layer="148" from-port="2" to-layer="149" to-port="0" />
		<edge from-layer="149" from-port="1" to-layer="152" to-port="0" />
		<edge from-layer="150" from-port="0" to-layer="151" to-port="0" />
		<edge from-layer="151" from-port="1" to-layer="152" to-port="1" />
		<edge from-layer="152" from-port="2" to-layer="153" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="171" to-port="0" />
		<edge from-layer="153" from-port="2" to-layer="156" to-port="0" />
		<edge from-layer="154" from-port="0" to-layer="156" to-port="1" />
		<edge from-layer="155" from-port="0" to-layer="156" to-port="2" />
		<edge from-layer="156" from-port="3" to-layer="159" to-port="0" />
		<edge from-layer="156" from-port="4" to-layer="160" to-port="1" />
		<edge from-layer="157" from-port="0" to-layer="158" to-port="0" />
		<edge from-layer="158" from-port="1" to-layer="159" to-port="1" />
		<edge from-layer="159" from-port="2" to-layer="160" to-port="0" />
		<edge from-layer="160" from-port="2" to-layer="163" to-port="0" />
		<edge from-layer="161" from-port="0" to-layer="162" to-port="0" />
		<edge from-layer="162" from-port="1" to-layer="163" to-port="1" />
		<edge from-layer="163" from-port="2" to-layer="166" to-port="0" />
		<edge from-layer="164" from-port="0" to-layer="165" to-port="0" />
		<edge from-layer="165" from-port="1" to-layer="166" to-port="1" />
		<edge from-layer="166" from-port="2" to-layer="167" to-port="0" />
		<edge from-layer="167" from-port="1" to-layer="170" to-port="0" />
		<edge from-layer="168" from-port="0" to-layer="169" to-port="0" />
		<edge from-layer="169" from-port="1" to-layer="170" to-port="1" />
		<edge from-layer="170" from-port="2" to-layer="171" to-port="1" />
		<edge from-layer="171" from-port="2" to-layer="174" to-port="0" />
		<edge from-layer="171" from-port="2" to-layer="189" to-port="0" />
		<edge from-layer="172" from-port="0" to-layer="174" to-port="1" />
		<edge from-layer="173" from-port="0" to-layer="174" to-port="2" />
		<edge from-layer="174" from-port="3" to-layer="177" to-port="0" />
		<edge from-layer="174" from-port="4" to-layer="178" to-port="1" />
		<edge from-layer="175" from-port="0" to-layer="176" to-port="0" />
		<edge from-layer="176" from-port="1" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="178" to-port="0" />
		<edge from-layer="178" from-port="2" to-layer="181" to-port="0" />
		<edge from-layer="179" from-port="0" to-layer="180" to-port="0" />
		<edge from-layer="180" from-port="1" to-layer="181" to-port="1" />
		<edge from-layer="181" from-port="2" to-layer="184" to-port="0" />
		<edge from-layer="182" from-port="0" to-layer="183" to-port="0" />
		<edge from-layer="183" from-port="1" to-layer="184" to-port="1" />
		<edge from-layer="184" from-port="2" to-layer="185" to-port="0" />
		<edge from-layer="185" from-port="1" to-layer="188" to-port="0" />
		<edge from-layer="186" from-port="0" to-layer="187" to-port="0" />
		<edge from-layer="187" from-port="1" to-layer="188" to-port="1" />
		<edge from-layer="188" from-port="2" to-layer="189" to-port="1" />
		<edge from-layer="189" from-port="2" to-layer="192" to-port="0" />
		<edge from-layer="189" from-port="2" to-layer="207" to-port="0" />
		<edge from-layer="190" from-port="0" to-layer="192" to-port="1" />
		<edge from-layer="191" from-port="0" to-layer="192" to-port="2" />
		<edge from-layer="192" from-port="3" to-layer="195" to-port="0" />
		<edge from-layer="192" from-port="4" to-layer="196" to-port="1" />
		<edge from-layer="193" from-port="0" to-layer="194" to-port="0" />
		<edge from-layer="194" from-port="1" to-layer="195" to-port="1" />
		<edge from-layer="195" from-port="2" to-layer="196" to-port="0" />
		<edge from-layer="196" from-port="2" to-layer="199" to-port="0" />
		<edge from-layer="197" from-port="0" to-layer="198" to-port="0" />
		<edge from-layer="198" from-port="1" to-layer="199" to-port="1" />
		<edge from-layer="199" from-port="2" to-layer="202" to-port="0" />
		<edge from-layer="200" from-port="0" to-layer="201" to-port="0" />
		<edge from-layer="201" from-port="1" to-layer="202" to-port="1" />
		<edge from-layer="202" from-port="2" to-layer="203" to-port="0" />
		<edge from-layer="203" from-port="1" to-layer="206" to-port="0" />
		<edge from-layer="204" from-port="0" to-layer="205" to-port="0" />
		<edge from-layer="205" from-port="1" to-layer="206" to-port="1" />
		<edge from-layer="206" from-port="2" to-layer="207" to-port="1" />
		<edge from-layer="207" from-port="2" to-layer="210" to-port="0" />
		<edge from-layer="207" from-port="2" to-layer="225" to-port="0" />
		<edge from-layer="208" from-port="0" to-layer="210" to-port="1" />
		<edge from-layer="209" from-port="0" to-layer="210" to-port="2" />
		<edge from-layer="210" from-port="3" to-layer="213" to-port="0" />
		<edge from-layer="210" from-port="4" to-layer="214" to-port="1" />
		<edge from-layer="211" from-port="0" to-layer="212" to-port="0" />
		<edge from-layer="212" from-port="1" to-layer="213" to-port="1" />
		<edge from-layer="213" from-port="2" to-layer="214" to-port="0" />
		<edge from-layer="214" from-port="2" to-layer="217" to-port="0" />
		<edge from-layer="215" from-port="0" to-layer="216" to-port="0" />
		<edge from-layer="216" from-port="1" to-layer="217" to-port="1" />
		<edge from-layer="217" from-port="2" to-layer="220" to-port="0" />
		<edge from-layer="218" from-port="0" to-layer="219" to-port="0" />
		<edge from-layer="219" from-port="1" to-layer="220" to-port="1" />
		<edge from-layer="220" from-port="2" to-layer="221" to-port="0" />
		<edge from-layer="221" from-port="1" to-layer="224" to-port="0" />
		<edge from-layer="222" from-port="0" to-layer="223" to-port="0" />
		<edge from-layer="223" from-port="1" to-layer="224" to-port="1" />
		<edge from-layer="224" from-port="2" to-layer="225" to-port="1" />
		<edge from-layer="225" from-port="2" to-layer="228" to-port="0" />
		<edge from-layer="225" from-port="2" to-layer="243" to-port="0" />
		<edge from-layer="226" from-port="0" to-layer="228" to-port="1" />
		<edge from-layer="227" from-port="0" to-layer="228" to-port="2" />
		<edge from-layer="228" from-port="3" to-layer="231" to-port="0" />
		<edge from-layer="228" from-port="4" to-layer="232" to-port="1" />
		<edge from-layer="229" from-port="0" to-layer="230" to-port="0" />
		<edge from-layer="230" from-port="1" to-layer="231" to-port="1" />
		<edge from-layer="231" from-port="2" to-layer="232" to-port="0" />
		<edge from-layer="232" from-port="2" to-layer="235" to-port="0" />
		<edge from-layer="233" from-port="0" to-layer="234" to-port="0" />
		<edge from-layer="234" from-port="1" to-layer="235" to-port="1" />
		<edge from-layer="235" from-port="2" to-layer="238" to-port="0" />
		<edge from-layer="236" from-port="0" to-layer="237" to-port="0" />
		<edge from-layer="237" from-port="1" to-layer="238" to-port="1" />
		<edge from-layer="238" from-port="2" to-layer="239" to-port="0" />
		<edge from-layer="239" from-port="1" to-layer="242" to-port="0" />
		<edge from-layer="240" from-port="0" to-layer="241" to-port="0" />
		<edge from-layer="241" from-port="1" to-layer="242" to-port="1" />
		<edge from-layer="242" from-port="2" to-layer="243" to-port="1" />
		<edge from-layer="243" from-port="2" to-layer="261" to-port="0" />
		<edge from-layer="243" from-port="2" to-layer="246" to-port="0" />
		<edge from-layer="244" from-port="0" to-layer="246" to-port="1" />
		<edge from-layer="245" from-port="0" to-layer="246" to-port="2" />
		<edge from-layer="246" from-port="3" to-layer="249" to-port="0" />
		<edge from-layer="246" from-port="4" to-layer="250" to-port="1" />
		<edge from-layer="247" from-port="0" to-layer="248" to-port="0" />
		<edge from-layer="248" from-port="1" to-layer="249" to-port="1" />
		<edge from-layer="249" from-port="2" to-layer="250" to-port="0" />
		<edge from-layer="250" from-port="2" to-layer="253" to-port="0" />
		<edge from-layer="251" from-port="0" to-layer="252" to-port="0" />
		<edge from-layer="252" from-port="1" to-layer="253" to-port="1" />
		<edge from-layer="253" from-port="2" to-layer="256" to-port="0" />
		<edge from-layer="254" from-port="0" to-layer="255" to-port="0" />
		<edge from-layer="255" from-port="1" to-layer="256" to-port="1" />
		<edge from-layer="256" from-port="2" to-layer="257" to-port="0" />
		<edge from-layer="257" from-port="1" to-layer="260" to-port="0" />
		<edge from-layer="258" from-port="0" to-layer="259" to-port="0" />
		<edge from-layer="259" from-port="1" to-layer="260" to-port="1" />
		<edge from-layer="260" from-port="2" to-layer="261" to-port="1" />
		<edge from-layer="261" from-port="2" to-layer="264" to-port="0" />
		<edge from-layer="261" from-port="2" to-layer="337" to-port="0" />
		<edge from-layer="262" from-port="0" to-layer="263" to-port="0" />
		<edge from-layer="263" from-port="1" to-layer="264" to-port="1" />
		<edge from-layer="264" from-port="2" to-layer="267" to-port="0" />
		<edge from-layer="265" from-port="0" to-layer="266" to-port="0" />
		<edge from-layer="266" from-port="1" to-layer="267" to-port="1" />
		<edge from-layer="267" from-port="2" to-layer="268" to-port="0" />
		<edge from-layer="268" from-port="1" to-layer="289" to-port="0" />
		<edge from-layer="268" from-port="1" to-layer="270" to-port="0" />
		<edge from-layer="269" from-port="0" to-layer="270" to-port="1" />
		<edge from-layer="270" from-port="2" to-layer="272" to-port="0" />
		<edge from-layer="271" from-port="0" to-layer="272" to-port="1" />
		<edge from-layer="272" from-port="2" to-layer="275" to-port="0" />
		<edge from-layer="273" from-port="0" to-layer="274" to-port="0" />
		<edge from-layer="274" from-port="1" to-layer="275" to-port="1" />
		<edge from-layer="275" from-port="2" to-layer="278" to-port="0" />
		<edge from-layer="276" from-port="0" to-layer="277" to-port="0" />
		<edge from-layer="277" from-port="1" to-layer="278" to-port="1" />
		<edge from-layer="278" from-port="2" to-layer="279" to-port="0" />
		<edge from-layer="279" from-port="1" to-layer="282" to-port="0" />
		<edge from-layer="280" from-port="0" to-layer="281" to-port="0" />
		<edge from-layer="281" from-port="1" to-layer="282" to-port="1" />
		<edge from-layer="282" from-port="2" to-layer="285" to-port="0" />
		<edge from-layer="283" from-port="0" to-layer="284" to-port="0" />
		<edge from-layer="284" from-port="1" to-layer="285" to-port="1" />
		<edge from-layer="285" from-port="2" to-layer="286" to-port="0" />
		<edge from-layer="286" from-port="1" to-layer="288" to-port="0" />
		<edge from-layer="287" from-port="0" to-layer="288" to-port="1" />
		<edge from-layer="288" from-port="2" to-layer="289" to-port="1" />
		<edge from-layer="289" from-port="2" to-layer="307" to-port="0" />
		<edge from-layer="289" from-port="2" to-layer="292" to-port="0" />
		<edge from-layer="290" from-port="0" to-layer="292" to-port="1" />
		<edge from-layer="291" from-port="0" to-layer="292" to-port="2" />
		<edge from-layer="292" from-port="3" to-layer="295" to-port="0" />
		<edge from-layer="292" from-port="4" to-layer="296" to-port="1" />
		<edge from-layer="293" from-port="0" to-layer="294" to-port="0" />
		<edge from-layer="294" from-port="1" to-layer="295" to-port="1" />
		<edge from-layer="295" from-port="2" to-layer="296" to-port="0" />
		<edge from-layer="296" from-port="2" to-layer="299" to-port="0" />
		<edge from-layer="297" from-port="0" to-layer="298" to-port="0" />
		<edge from-layer="298" from-port="1" to-layer="299" to-port="1" />
		<edge from-layer="299" from-port="2" to-layer="302" to-port="0" />
		<edge from-layer="300" from-port="0" to-layer="301" to-port="0" />
		<edge from-layer="301" from-port="1" to-layer="302" to-port="1" />
		<edge from-layer="302" from-port="2" to-layer="303" to-port="0" />
		<edge from-layer="303" from-port="1" to-layer="306" to-port="0" />
		<edge from-layer="304" from-port="0" to-layer="305" to-port="0" />
		<edge from-layer="305" from-port="1" to-layer="306" to-port="1" />
		<edge from-layer="306" from-port="2" to-layer="307" to-port="1" />
		<edge from-layer="307" from-port="2" to-layer="310" to-port="0" />
		<edge from-layer="307" from-port="2" to-layer="325" to-port="0" />
		<edge from-layer="308" from-port="0" to-layer="310" to-port="1" />
		<edge from-layer="309" from-port="0" to-layer="310" to-port="2" />
		<edge from-layer="310" from-port="4" to-layer="314" to-port="1" />
		<edge from-layer="310" from-port="3" to-layer="313" to-port="0" />
		<edge from-layer="311" from-port="0" to-layer="312" to-port="0" />
		<edge from-layer="312" from-port="1" to-layer="313" to-port="1" />
		<edge from-layer="313" from-port="2" to-layer="314" to-port="0" />
		<edge from-layer="314" from-port="2" to-layer="317" to-port="0" />
		<edge from-layer="315" from-port="0" to-layer="316" to-port="0" />
		<edge from-layer="316" from-port="1" to-layer="317" to-port="1" />
		<edge from-layer="317" from-port="2" to-layer="320" to-port="0" />
		<edge from-layer="318" from-port="0" to-layer="319" to-port="0" />
		<edge from-layer="319" from-port="1" to-layer="320" to-port="1" />
		<edge from-layer="320" from-port="2" to-layer="321" to-port="0" />
		<edge from-layer="321" from-port="1" to-layer="324" to-port="0" />
		<edge from-layer="322" from-port="0" to-layer="323" to-port="0" />
		<edge from-layer="323" from-port="1" to-layer="324" to-port="1" />
		<edge from-layer="324" from-port="2" to-layer="325" to-port="1" />
		<edge from-layer="325" from-port="2" to-layer="328" to-port="0" />
		<edge from-layer="326" from-port="0" to-layer="327" to-port="0" />
		<edge from-layer="327" from-port="1" to-layer="328" to-port="1" />
		<edge from-layer="328" from-port="2" to-layer="331" to-port="0" />
		<edge from-layer="329" from-port="0" to-layer="330" to-port="0" />
		<edge from-layer="330" from-port="1" to-layer="331" to-port="1" />
		<edge from-layer="331" from-port="2" to-layer="332" to-port="0" />
		<edge from-layer="332" from-port="1" to-layer="629" to-port="1" />
		<edge from-layer="332" from-port="1" to-layer="334" to-port="0" />
		<edge from-layer="333" from-port="0" to-layer="334" to-port="1" />
		<edge from-layer="333" from-port="0" to-layer="384" to-port="1" />
		<edge from-layer="334" from-port="2" to-layer="342" to-port="0" />
		<edge from-layer="335" from-port="0" to-layer="336" to-port="0" />
		<edge from-layer="336" from-port="1" to-layer="337" to-port="1" />
		<edge from-layer="337" from-port="2" to-layer="340" to-port="0" />
		<edge from-layer="338" from-port="0" to-layer="339" to-port="0" />
		<edge from-layer="339" from-port="1" to-layer="340" to-port="1" />
		<edge from-layer="340" from-port="2" to-layer="341" to-port="0" />
		<edge from-layer="341" from-port="1" to-layer="342" to-port="1" />
		<edge from-layer="342" from-port="2" to-layer="373" to-port="0" />
		<edge from-layer="342" from-port="2" to-layer="345" to-port="0" />
		<edge from-layer="343" from-port="0" to-layer="344" to-port="0" />
		<edge from-layer="344" from-port="1" to-layer="345" to-port="1" />
		<edge from-layer="345" from-port="2" to-layer="348" to-port="0" />
		<edge from-layer="346" from-port="0" to-layer="347" to-port="0" />
		<edge from-layer="347" from-port="1" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="349" to-port="0" />
		<edge from-layer="349" from-port="1" to-layer="352" to-port="0" />
		<edge from-layer="349" from-port="1" to-layer="357" to-port="0" />
		<edge from-layer="350" from-port="0" to-layer="351" to-port="0" />
		<edge from-layer="351" from-port="1" to-layer="352" to-port="1" />
		<edge from-layer="352" from-port="2" to-layer="355" to-port="0" />
		<edge from-layer="353" from-port="0" to-layer="354" to-port="0" />
		<edge from-layer="354" from-port="1" to-layer="355" to-port="1" />
		<edge from-layer="355" from-port="2" to-layer="356" to-port="0" />
		<edge from-layer="356" from-port="1" to-layer="357" to-port="1" />
		<edge from-layer="357" from-port="2" to-layer="360" to-port="0" />
		<edge from-layer="358" from-port="0" to-layer="359" to-port="0" />
		<edge from-layer="359" from-port="1" to-layer="360" to-port="1" />
		<edge from-layer="360" from-port="2" to-layer="363" to-port="0" />
		<edge from-layer="361" from-port="0" to-layer="362" to-port="0" />
		<edge from-layer="362" from-port="1" to-layer="363" to-port="1" />
		<edge from-layer="363" from-port="2" to-layer="366" to-port="0" />
		<edge from-layer="363" from-port="2" to-layer="370" to-port="0" />
		<edge from-layer="364" from-port="0" to-layer="365" to-port="0" />
		<edge from-layer="365" from-port="1" to-layer="366" to-port="1" />
		<edge from-layer="366" from-port="2" to-layer="369" to-port="0" />
		<edge from-layer="367" from-port="0" to-layer="368" to-port="0" />
		<edge from-layer="368" from-port="1" to-layer="369" to-port="1" />
		<edge from-layer="369" from-port="2" to-layer="370" to-port="1" />
		<edge from-layer="370" from-port="2" to-layer="383" to-port="0" />
		<edge from-layer="371" from-port="0" to-layer="372" to-port="0" />
		<edge from-layer="372" from-port="1" to-layer="373" to-port="1" />
		<edge from-layer="373" from-port="2" to-layer="376" to-port="0" />
		<edge from-layer="374" from-port="0" to-layer="375" to-port="0" />
		<edge from-layer="375" from-port="1" to-layer="376" to-port="1" />
		<edge from-layer="376" from-port="2" to-layer="379" to-port="0" />
		<edge from-layer="377" from-port="0" to-layer="378" to-port="0" />
		<edge from-layer="378" from-port="1" to-layer="379" to-port="1" />
		<edge from-layer="379" from-port="2" to-layer="382" to-port="0" />
		<edge from-layer="380" from-port="0" to-layer="381" to-port="0" />
		<edge from-layer="381" from-port="1" to-layer="382" to-port="1" />
		<edge from-layer="382" from-port="2" to-layer="383" to-port="1" />
		<edge from-layer="383" from-port="2" to-layer="511" to-port="1" />
		<edge from-layer="383" from-port="2" to-layer="384" to-port="0" />
		<edge from-layer="384" from-port="2" to-layer="392" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="386" to-port="0" />
		<edge from-layer="386" from-port="1" to-layer="387" to-port="1" />
		<edge from-layer="387" from-port="2" to-layer="390" to-port="0" />
		<edge from-layer="388" from-port="0" to-layer="389" to-port="0" />
		<edge from-layer="389" from-port="1" to-layer="390" to-port="1" />
		<edge from-layer="390" from-port="2" to-layer="391" to-port="0" />
		<edge from-layer="391" from-port="1" to-layer="392" to-port="1" />
		<edge from-layer="392" from-port="2" to-layer="423" to-port="0" />
		<edge from-layer="392" from-port="2" to-layer="395" to-port="0" />
		<edge from-layer="393" from-port="0" to-layer="394" to-port="0" />
		<edge from-layer="394" from-port="1" to-layer="395" to-port="1" />
		<edge from-layer="395" from-port="2" to-layer="398" to-port="0" />
		<edge from-layer="396" from-port="0" to-layer="397" to-port="0" />
		<edge from-layer="397" from-port="1" to-layer="398" to-port="1" />
		<edge from-layer="398" from-port="2" to-layer="399" to-port="0" />
		<edge from-layer="399" from-port="1" to-layer="402" to-port="0" />
		<edge from-layer="399" from-port="1" to-layer="407" to-port="0" />
		<edge from-layer="400" from-port="0" to-layer="401" to-port="0" />
		<edge from-layer="401" from-port="1" to-layer="402" to-port="1" />
		<edge from-layer="402" from-port="2" to-layer="405" to-port="0" />
		<edge from-layer="403" from-port="0" to-layer="404" to-port="0" />
		<edge from-layer="404" from-port="1" to-layer="405" to-port="1" />
		<edge from-layer="405" from-port="2" to-layer="406" to-port="0" />
		<edge from-layer="406" from-port="1" to-layer="407" to-port="1" />
		<edge from-layer="407" from-port="2" to-layer="410" to-port="0" />
		<edge from-layer="408" from-port="0" to-layer="409" to-port="0" />
		<edge from-layer="409" from-port="1" to-layer="410" to-port="1" />
		<edge from-layer="410" from-port="2" to-layer="413" to-port="0" />
		<edge from-layer="411" from-port="0" to-layer="412" to-port="0" />
		<edge from-layer="412" from-port="1" to-layer="413" to-port="1" />
		<edge from-layer="413" from-port="2" to-layer="416" to-port="0" />
		<edge from-layer="413" from-port="2" to-layer="420" to-port="0" />
		<edge from-layer="414" from-port="0" to-layer="415" to-port="0" />
		<edge from-layer="415" from-port="1" to-layer="416" to-port="1" />
		<edge from-layer="416" from-port="2" to-layer="419" to-port="0" />
		<edge from-layer="417" from-port="0" to-layer="418" to-port="0" />
		<edge from-layer="418" from-port="1" to-layer="419" to-port="1" />
		<edge from-layer="419" from-port="2" to-layer="420" to-port="1" />
		<edge from-layer="420" from-port="2" to-layer="433" to-port="0" />
		<edge from-layer="421" from-port="0" to-layer="422" to-port="0" />
		<edge from-layer="422" from-port="1" to-layer="423" to-port="1" />
		<edge from-layer="423" from-port="2" to-layer="426" to-port="0" />
		<edge from-layer="424" from-port="0" to-layer="425" to-port="0" />
		<edge from-layer="425" from-port="1" to-layer="426" to-port="1" />
		<edge from-layer="426" from-port="2" to-layer="429" to-port="0" />
		<edge from-layer="427" from-port="0" to-layer="428" to-port="0" />
		<edge from-layer="428" from-port="1" to-layer="429" to-port="1" />
		<edge from-layer="429" from-port="2" to-layer="432" to-port="0" />
		<edge from-layer="430" from-port="0" to-layer="431" to-port="0" />
		<edge from-layer="431" from-port="1" to-layer="432" to-port="1" />
		<edge from-layer="432" from-port="2" to-layer="433" to-port="1" />
		<edge from-layer="433" from-port="2" to-layer="500" to-port="0" />
		<edge from-layer="433" from-port="2" to-layer="436" to-port="0" />
		<edge from-layer="434" from-port="0" to-layer="435" to-port="0" />
		<edge from-layer="435" from-port="1" to-layer="436" to-port="1" />
		<edge from-layer="436" from-port="2" to-layer="439" to-port="0" />
		<edge from-layer="437" from-port="0" to-layer="438" to-port="0" />
		<edge from-layer="438" from-port="1" to-layer="439" to-port="1" />
		<edge from-layer="439" from-port="2" to-layer="440" to-port="0" />
		<edge from-layer="440" from-port="1" to-layer="443" to-port="0" />
		<edge from-layer="441" from-port="0" to-layer="442" to-port="0" />
		<edge from-layer="442" from-port="1" to-layer="443" to-port="1" />
		<edge from-layer="443" from-port="2" to-layer="446" to-port="0" />
		<edge from-layer="444" from-port="0" to-layer="445" to-port="0" />
		<edge from-layer="445" from-port="1" to-layer="446" to-port="1" />
		<edge from-layer="446" from-port="2" to-layer="449" to-port="0" />
		<edge from-layer="447" from-port="0" to-layer="448" to-port="0" />
		<edge from-layer="448" from-port="1" to-layer="449" to-port="1" />
		<edge from-layer="449" from-port="2" to-layer="452" to-port="0" />
		<edge from-layer="450" from-port="0" to-layer="451" to-port="0" />
		<edge from-layer="451" from-port="1" to-layer="452" to-port="1" />
		<edge from-layer="452" from-port="2" to-layer="453" to-port="0" />
		<edge from-layer="453" from-port="1" to-layer="456" to-port="0" />
		<edge from-layer="454" from-port="0" to-layer="455" to-port="0" />
		<edge from-layer="455" from-port="1" to-layer="456" to-port="1" />
		<edge from-layer="456" from-port="2" to-layer="459" to-port="0" />
		<edge from-layer="457" from-port="0" to-layer="458" to-port="0" />
		<edge from-layer="458" from-port="1" to-layer="459" to-port="1" />
		<edge from-layer="459" from-port="2" to-layer="462" to-port="0" />
		<edge from-layer="460" from-port="0" to-layer="461" to-port="0" />
		<edge from-layer="461" from-port="1" to-layer="462" to-port="1" />
		<edge from-layer="462" from-port="2" to-layer="465" to-port="0" />
		<edge from-layer="463" from-port="0" to-layer="464" to-port="0" />
		<edge from-layer="464" from-port="1" to-layer="465" to-port="1" />
		<edge from-layer="465" from-port="2" to-layer="466" to-port="0" />
		<edge from-layer="466" from-port="1" to-layer="470" to-port="0" />
		<edge from-layer="466" from-port="1" to-layer="487" to-port="0" />
		<edge from-layer="467" from-port="0" to-layer="470" to-port="1" />
		<edge from-layer="468" from-port="0" to-layer="470" to-port="2" />
		<edge from-layer="469" from-port="0" to-layer="470" to-port="3" />
		<edge from-layer="470" from-port="4" to-layer="473" to-port="0" />
		<edge from-layer="470" from-port="4" to-layer="479" to-port="0" />
		<edge from-layer="471" from-port="0" to-layer="472" to-port="0" />
		<edge from-layer="472" from-port="1" to-layer="473" to-port="1" />
		<edge from-layer="473" from-port="2" to-layer="476" to-port="0" />
		<edge from-layer="474" from-port="0" to-layer="475" to-port="0" />
		<edge from-layer="475" from-port="1" to-layer="476" to-port="1" />
		<edge from-layer="476" from-port="2" to-layer="495" to-port="0" />
		<edge from-layer="477" from-port="0" to-layer="478" to-port="0" />
		<edge from-layer="478" from-port="1" to-layer="479" to-port="1" />
		<edge from-layer="479" from-port="2" to-layer="482" to-port="0" />
		<edge from-layer="480" from-port="0" to-layer="481" to-port="0" />
		<edge from-layer="481" from-port="1" to-layer="482" to-port="1" />
		<edge from-layer="482" from-port="2" to-layer="483" to-port="0" />
		<edge from-layer="483" from-port="1" to-layer="495" to-port="1" />
		<edge from-layer="484" from-port="0" to-layer="487" to-port="1" />
		<edge from-layer="485" from-port="0" to-layer="487" to-port="2" />
		<edge from-layer="486" from-port="0" to-layer="487" to-port="3" />
		<edge from-layer="487" from-port="4" to-layer="490" to-port="0" />
		<edge from-layer="488" from-port="0" to-layer="489" to-port="0" />
		<edge from-layer="489" from-port="1" to-layer="490" to-port="1" />
		<edge from-layer="490" from-port="2" to-layer="493" to-port="0" />
		<edge from-layer="491" from-port="0" to-layer="492" to-port="0" />
		<edge from-layer="492" from-port="1" to-layer="493" to-port="1" />
		<edge from-layer="493" from-port="2" to-layer="494" to-port="0" />
		<edge from-layer="494" from-port="1" to-layer="495" to-port="2" />
		<edge from-layer="495" from-port="3" to-layer="497" to-port="0" />
		<edge from-layer="496" from-port="0" to-layer="733" to-port="1" />
		<edge from-layer="496" from-port="0" to-layer="615" to-port="1" />
		<edge from-layer="496" from-port="0" to-layer="497" to-port="1" />
		<edge from-layer="497" from-port="2" to-layer="734" to-port="0" />
		<edge from-layer="498" from-port="0" to-layer="499" to-port="0" />
		<edge from-layer="499" from-port="1" to-layer="500" to-port="1" />
		<edge from-layer="500" from-port="2" to-layer="503" to-port="0" />
		<edge from-layer="501" from-port="0" to-layer="502" to-port="0" />
		<edge from-layer="502" from-port="1" to-layer="503" to-port="1" />
		<edge from-layer="503" from-port="2" to-layer="506" to-port="0" />
		<edge from-layer="504" from-port="0" to-layer="505" to-port="0" />
		<edge from-layer="505" from-port="1" to-layer="506" to-port="1" />
		<edge from-layer="506" from-port="2" to-layer="509" to-port="0" />
		<edge from-layer="507" from-port="0" to-layer="508" to-port="0" />
		<edge from-layer="508" from-port="1" to-layer="509" to-port="1" />
		<edge from-layer="509" from-port="2" to-layer="510" to-port="0" />
		<edge from-layer="510" from-port="1" to-layer="511" to-port="0" />
		<edge from-layer="511" from-port="2" to-layer="514" to-port="0" />
		<edge from-layer="511" from-port="2" to-layer="542" to-port="0" />
		<edge from-layer="512" from-port="0" to-layer="513" to-port="0" />
		<edge from-layer="513" from-port="1" to-layer="514" to-port="1" />
		<edge from-layer="514" from-port="2" to-layer="517" to-port="0" />
		<edge from-layer="515" from-port="0" to-layer="516" to-port="0" />
		<edge from-layer="516" from-port="1" to-layer="517" to-port="1" />
		<edge from-layer="517" from-port="2" to-layer="518" to-port="0" />
		<edge from-layer="518" from-port="1" to-layer="526" to-port="0" />
		<edge from-layer="518" from-port="1" to-layer="521" to-port="0" />
		<edge from-layer="519" from-port="0" to-layer="520" to-port="0" />
		<edge from-layer="520" from-port="1" to-layer="521" to-port="1" />
		<edge from-layer="521" from-port="2" to-layer="524" to-port="0" />
		<edge from-layer="522" from-port="0" to-layer="523" to-port="0" />
		<edge from-layer="523" from-port="1" to-layer="524" to-port="1" />
		<edge from-layer="524" from-port="2" to-layer="525" to-port="0" />
		<edge from-layer="525" from-port="1" to-layer="526" to-port="1" />
		<edge from-layer="526" from-port="2" to-layer="529" to-port="0" />
		<edge from-layer="527" from-port="0" to-layer="528" to-port="0" />
		<edge from-layer="528" from-port="1" to-layer="529" to-port="1" />
		<edge from-layer="529" from-port="2" to-layer="532" to-port="0" />
		<edge from-layer="530" from-port="0" to-layer="531" to-port="0" />
		<edge from-layer="531" from-port="1" to-layer="532" to-port="1" />
		<edge from-layer="532" from-port="2" to-layer="539" to-port="0" />
		<edge from-layer="532" from-port="2" to-layer="535" to-port="0" />
		<edge from-layer="533" from-port="0" to-layer="534" to-port="0" />
		<edge from-layer="534" from-port="1" to-layer="535" to-port="1" />
		<edge from-layer="535" from-port="2" to-layer="538" to-port="0" />
		<edge from-layer="536" from-port="0" to-layer="537" to-port="0" />
		<edge from-layer="537" from-port="1" to-layer="538" to-port="1" />
		<edge from-layer="538" from-port="2" to-layer="539" to-port="1" />
		<edge from-layer="539" from-port="2" to-layer="552" to-port="0" />
		<edge from-layer="540" from-port="0" to-layer="541" to-port="0" />
		<edge from-layer="541" from-port="1" to-layer="542" to-port="1" />
		<edge from-layer="542" from-port="2" to-layer="545" to-port="0" />
		<edge from-layer="543" from-port="0" to-layer="544" to-port="0" />
		<edge from-layer="544" from-port="1" to-layer="545" to-port="1" />
		<edge from-layer="545" from-port="2" to-layer="548" to-port="0" />
		<edge from-layer="546" from-port="0" to-layer="547" to-port="0" />
		<edge from-layer="547" from-port="1" to-layer="548" to-port="1" />
		<edge from-layer="548" from-port="2" to-layer="551" to-port="0" />
		<edge from-layer="549" from-port="0" to-layer="550" to-port="0" />
		<edge from-layer="550" from-port="1" to-layer="551" to-port="1" />
		<edge from-layer="551" from-port="2" to-layer="552" to-port="1" />
		<edge from-layer="552" from-port="2" to-layer="555" to-port="0" />
		<edge from-layer="552" from-port="2" to-layer="618" to-port="0" />
		<edge from-layer="553" from-port="0" to-layer="554" to-port="0" />
		<edge from-layer="554" from-port="1" to-layer="555" to-port="1" />
		<edge from-layer="555" from-port="2" to-layer="558" to-port="0" />
		<edge from-layer="556" from-port="0" to-layer="557" to-port="0" />
		<edge from-layer="557" from-port="1" to-layer="558" to-port="1" />
		<edge from-layer="558" from-port="2" to-layer="559" to-port="0" />
		<edge from-layer="559" from-port="1" to-layer="562" to-port="0" />
		<edge from-layer="560" from-port="0" to-layer="561" to-port="0" />
		<edge from-layer="561" from-port="1" to-layer="562" to-port="1" />
		<edge from-layer="562" from-port="2" to-layer="565" to-port="0" />
		<edge from-layer="563" from-port="0" to-layer="564" to-port="0" />
		<edge from-layer="564" from-port="1" to-layer="565" to-port="1" />
		<edge from-layer="565" from-port="2" to-layer="568" to-port="0" />
		<edge from-layer="566" from-port="0" to-layer="567" to-port="0" />
		<edge from-layer="567" from-port="1" to-layer="568" to-port="1" />
		<edge from-layer="568" from-port="2" to-layer="571" to-port="0" />
		<edge from-layer="569" from-port="0" to-layer="570" to-port="0" />
		<edge from-layer="570" from-port="1" to-layer="571" to-port="1" />
		<edge from-layer="571" from-port="2" to-layer="572" to-port="0" />
		<edge from-layer="572" from-port="1" to-layer="575" to-port="0" />
		<edge from-layer="573" from-port="0" to-layer="574" to-port="0" />
		<edge from-layer="574" from-port="1" to-layer="575" to-port="1" />
		<edge from-layer="575" from-port="2" to-layer="578" to-port="0" />
		<edge from-layer="576" from-port="0" to-layer="577" to-port="0" />
		<edge from-layer="577" from-port="1" to-layer="578" to-port="1" />
		<edge from-layer="578" from-port="2" to-layer="581" to-port="0" />
		<edge from-layer="579" from-port="0" to-layer="580" to-port="0" />
		<edge from-layer="580" from-port="1" to-layer="581" to-port="1" />
		<edge from-layer="581" from-port="2" to-layer="584" to-port="0" />
		<edge from-layer="582" from-port="0" to-layer="583" to-port="0" />
		<edge from-layer="583" from-port="1" to-layer="584" to-port="1" />
		<edge from-layer="584" from-port="2" to-layer="585" to-port="0" />
		<edge from-layer="585" from-port="1" to-layer="589" to-port="0" />
		<edge from-layer="585" from-port="1" to-layer="606" to-port="0" />
		<edge from-layer="586" from-port="0" to-layer="589" to-port="1" />
		<edge from-layer="587" from-port="0" to-layer="589" to-port="2" />
		<edge from-layer="588" from-port="0" to-layer="589" to-port="3" />
		<edge from-layer="589" from-port="4" to-layer="598" to-port="0" />
		<edge from-layer="589" from-port="4" to-layer="592" to-port="0" />
		<edge from-layer="590" from-port="0" to-layer="591" to-port="0" />
		<edge from-layer="591" from-port="1" to-layer="592" to-port="1" />
		<edge from-layer="592" from-port="2" to-layer="595" to-port="0" />
		<edge from-layer="593" from-port="0" to-layer="594" to-port="0" />
		<edge from-layer="594" from-port="1" to-layer="595" to-port="1" />
		<edge from-layer="595" from-port="2" to-layer="614" to-port="0" />
		<edge from-layer="596" from-port="0" to-layer="597" to-port="0" />
		<edge from-layer="597" from-port="1" to-layer="598" to-port="1" />
		<edge from-layer="598" from-port="2" to-layer="601" to-port="0" />
		<edge from-layer="599" from-port="0" to-layer="600" to-port="0" />
		<edge from-layer="600" from-port="1" to-layer="601" to-port="1" />
		<edge from-layer="601" from-port="2" to-layer="602" to-port="0" />
		<edge from-layer="602" from-port="1" to-layer="614" to-port="1" />
		<edge from-layer="603" from-port="0" to-layer="606" to-port="1" />
		<edge from-layer="604" from-port="0" to-layer="606" to-port="2" />
		<edge from-layer="605" from-port="0" to-layer="606" to-port="3" />
		<edge from-layer="606" from-port="4" to-layer="609" to-port="0" />
		<edge from-layer="607" from-port="0" to-layer="608" to-port="0" />
		<edge from-layer="608" from-port="1" to-layer="609" to-port="1" />
		<edge from-layer="609" from-port="2" to-layer="612" to-port="0" />
		<edge from-layer="610" from-port="0" to-layer="611" to-port="0" />
		<edge from-layer="611" from-port="1" to-layer="612" to-port="1" />
		<edge from-layer="612" from-port="2" to-layer="613" to-port="0" />
		<edge from-layer="613" from-port="1" to-layer="614" to-port="2" />
		<edge from-layer="614" from-port="3" to-layer="615" to-port="0" />
		<edge from-layer="615" from-port="2" to-layer="734" to-port="1" />
		<edge from-layer="616" from-port="0" to-layer="617" to-port="0" />
		<edge from-layer="617" from-port="1" to-layer="618" to-port="1" />
		<edge from-layer="618" from-port="2" to-layer="621" to-port="0" />
		<edge from-layer="619" from-port="0" to-layer="620" to-port="0" />
		<edge from-layer="620" from-port="1" to-layer="621" to-port="1" />
		<edge from-layer="621" from-port="2" to-layer="624" to-port="0" />
		<edge from-layer="622" from-port="0" to-layer="623" to-port="0" />
		<edge from-layer="623" from-port="1" to-layer="624" to-port="1" />
		<edge from-layer="624" from-port="2" to-layer="627" to-port="0" />
		<edge from-layer="625" from-port="0" to-layer="626" to-port="0" />
		<edge from-layer="626" from-port="1" to-layer="627" to-port="1" />
		<edge from-layer="627" from-port="2" to-layer="628" to-port="0" />
		<edge from-layer="628" from-port="1" to-layer="629" to-port="0" />
		<edge from-layer="629" from-port="2" to-layer="660" to-port="0" />
		<edge from-layer="629" from-port="2" to-layer="632" to-port="0" />
		<edge from-layer="630" from-port="0" to-layer="631" to-port="0" />
		<edge from-layer="631" from-port="1" to-layer="632" to-port="1" />
		<edge from-layer="632" from-port="2" to-layer="635" to-port="0" />
		<edge from-layer="633" from-port="0" to-layer="634" to-port="0" />
		<edge from-layer="634" from-port="1" to-layer="635" to-port="1" />
		<edge from-layer="635" from-port="2" to-layer="636" to-port="0" />
		<edge from-layer="636" from-port="1" to-layer="644" to-port="0" />
		<edge from-layer="636" from-port="1" to-layer="639" to-port="0" />
		<edge from-layer="637" from-port="0" to-layer="638" to-port="0" />
		<edge from-layer="638" from-port="1" to-layer="639" to-port="1" />
		<edge from-layer="639" from-port="2" to-layer="642" to-port="0" />
		<edge from-layer="640" from-port="0" to-layer="641" to-port="0" />
		<edge from-layer="641" from-port="1" to-layer="642" to-port="1" />
		<edge from-layer="642" from-port="2" to-layer="643" to-port="0" />
		<edge from-layer="643" from-port="1" to-layer="644" to-port="1" />
		<edge from-layer="644" from-port="2" to-layer="647" to-port="0" />
		<edge from-layer="645" from-port="0" to-layer="646" to-port="0" />
		<edge from-layer="646" from-port="1" to-layer="647" to-port="1" />
		<edge from-layer="647" from-port="2" to-layer="650" to-port="0" />
		<edge from-layer="648" from-port="0" to-layer="649" to-port="0" />
		<edge from-layer="649" from-port="1" to-layer="650" to-port="1" />
		<edge from-layer="650" from-port="2" to-layer="657" to-port="0" />
		<edge from-layer="650" from-port="2" to-layer="653" to-port="0" />
		<edge from-layer="651" from-port="0" to-layer="652" to-port="0" />
		<edge from-layer="652" from-port="1" to-layer="653" to-port="1" />
		<edge from-layer="653" from-port="2" to-layer="656" to-port="0" />
		<edge from-layer="654" from-port="0" to-layer="655" to-port="0" />
		<edge from-layer="655" from-port="1" to-layer="656" to-port="1" />
		<edge from-layer="656" from-port="2" to-layer="657" to-port="1" />
		<edge from-layer="657" from-port="2" to-layer="670" to-port="0" />
		<edge from-layer="658" from-port="0" to-layer="659" to-port="0" />
		<edge from-layer="659" from-port="1" to-layer="660" to-port="1" />
		<edge from-layer="660" from-port="2" to-layer="663" to-port="0" />
		<edge from-layer="661" from-port="0" to-layer="662" to-port="0" />
		<edge from-layer="662" from-port="1" to-layer="663" to-port="1" />
		<edge from-layer="663" from-port="2" to-layer="666" to-port="0" />
		<edge from-layer="664" from-port="0" to-layer="665" to-port="0" />
		<edge from-layer="665" from-port="1" to-layer="666" to-port="1" />
		<edge from-layer="666" from-port="2" to-layer="669" to-port="0" />
		<edge from-layer="667" from-port="0" to-layer="668" to-port="0" />
		<edge from-layer="668" from-port="1" to-layer="669" to-port="1" />
		<edge from-layer="669" from-port="2" to-layer="670" to-port="1" />
		<edge from-layer="670" from-port="2" to-layer="673" to-port="0" />
		<edge from-layer="671" from-port="0" to-layer="672" to-port="0" />
		<edge from-layer="672" from-port="1" to-layer="673" to-port="1" />
		<edge from-layer="673" from-port="2" to-layer="676" to-port="0" />
		<edge from-layer="674" from-port="0" to-layer="675" to-port="0" />
		<edge from-layer="675" from-port="1" to-layer="676" to-port="1" />
		<edge from-layer="676" from-port="2" to-layer="677" to-port="0" />
		<edge from-layer="677" from-port="1" to-layer="680" to-port="0" />
		<edge from-layer="678" from-port="0" to-layer="679" to-port="0" />
		<edge from-layer="679" from-port="1" to-layer="680" to-port="1" />
		<edge from-layer="680" from-port="2" to-layer="683" to-port="0" />
		<edge from-layer="681" from-port="0" to-layer="682" to-port="0" />
		<edge from-layer="682" from-port="1" to-layer="683" to-port="1" />
		<edge from-layer="683" from-port="2" to-layer="686" to-port="0" />
		<edge from-layer="684" from-port="0" to-layer="685" to-port="0" />
		<edge from-layer="685" from-port="1" to-layer="686" to-port="1" />
		<edge from-layer="686" from-port="2" to-layer="689" to-port="0" />
		<edge from-layer="687" from-port="0" to-layer="688" to-port="0" />
		<edge from-layer="688" from-port="1" to-layer="689" to-port="1" />
		<edge from-layer="689" from-port="2" to-layer="690" to-port="0" />
		<edge from-layer="690" from-port="1" to-layer="693" to-port="0" />
		<edge from-layer="691" from-port="0" to-layer="692" to-port="0" />
		<edge from-layer="692" from-port="1" to-layer="693" to-port="1" />
		<edge from-layer="693" from-port="2" to-layer="696" to-port="0" />
		<edge from-layer="694" from-port="0" to-layer="695" to-port="0" />
		<edge from-layer="695" from-port="1" to-layer="696" to-port="1" />
		<edge from-layer="696" from-port="2" to-layer="699" to-port="0" />
		<edge from-layer="697" from-port="0" to-layer="698" to-port="0" />
		<edge from-layer="698" from-port="1" to-layer="699" to-port="1" />
		<edge from-layer="699" from-port="2" to-layer="702" to-port="0" />
		<edge from-layer="700" from-port="0" to-layer="701" to-port="0" />
		<edge from-layer="701" from-port="1" to-layer="702" to-port="1" />
		<edge from-layer="702" from-port="2" to-layer="703" to-port="0" />
		<edge from-layer="703" from-port="1" to-layer="707" to-port="0" />
		<edge from-layer="703" from-port="1" to-layer="724" to-port="0" />
		<edge from-layer="704" from-port="0" to-layer="707" to-port="1" />
		<edge from-layer="705" from-port="0" to-layer="707" to-port="2" />
		<edge from-layer="706" from-port="0" to-layer="707" to-port="3" />
		<edge from-layer="707" from-port="4" to-layer="716" to-port="0" />
		<edge from-layer="707" from-port="4" to-layer="710" to-port="0" />
		<edge from-layer="708" from-port="0" to-layer="709" to-port="0" />
		<edge from-layer="709" from-port="1" to-layer="710" to-port="1" />
		<edge from-layer="710" from-port="2" to-layer="713" to-port="0" />
		<edge from-layer="711" from-port="0" to-layer="712" to-port="0" />
		<edge from-layer="712" from-port="1" to-layer="713" to-port="1" />
		<edge from-layer="713" from-port="2" to-layer="732" to-port="0" />
		<edge from-layer="714" from-port="0" to-layer="715" to-port="0" />
		<edge from-layer="715" from-port="1" to-layer="716" to-port="1" />
		<edge from-layer="716" from-port="2" to-layer="719" to-port="0" />
		<edge from-layer="717" from-port="0" to-layer="718" to-port="0" />
		<edge from-layer="718" from-port="1" to-layer="719" to-port="1" />
		<edge from-layer="719" from-port="2" to-layer="720" to-port="0" />
		<edge from-layer="720" from-port="1" to-layer="732" to-port="1" />
		<edge from-layer="721" from-port="0" to-layer="724" to-port="1" />
		<edge from-layer="722" from-port="0" to-layer="724" to-port="2" />
		<edge from-layer="723" from-port="0" to-layer="724" to-port="3" />
		<edge from-layer="724" from-port="4" to-layer="727" to-port="0" />
		<edge from-layer="725" from-port="0" to-layer="726" to-port="0" />
		<edge from-layer="726" from-port="1" to-layer="727" to-port="1" />
		<edge from-layer="727" from-port="2" to-layer="730" to-port="0" />
		<edge from-layer="728" from-port="0" to-layer="729" to-port="0" />
		<edge from-layer="729" from-port="1" to-layer="730" to-port="1" />
		<edge from-layer="730" from-port="2" to-layer="731" to-port="0" />
		<edge from-layer="731" from-port="1" to-layer="732" to-port="2" />
		<edge from-layer="732" from-port="3" to-layer="733" to-port="0" />
		<edge from-layer="733" from-port="2" to-layer="734" to-port="2" />
		<edge from-layer="734" from-port="3" to-layer="736" to-port="0" />
		<edge from-layer="735" from-port="0" to-layer="736" to-port="1" />
		<edge from-layer="736" from-port="2" to-layer="737" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2024.0.0-14509-34caeefd078-releases/2024/0" />
		<Runtime_version value="2024.0.0-14509-34caeefd078-releases/2024/0" />
		<conversion_parameters>
			<input_model value="DIR/yolox_rune.onnx" />
			<is_python_api_used value="False" />
		</conversion_parameters>
		<legacy_frontend value="False" />
	</rt_info>
</net>
