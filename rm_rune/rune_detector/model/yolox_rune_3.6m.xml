<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="images" type="Parameter" version="opset1">
			<data shape="1,3,480,480" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="images">
					<dim>1</dim>
					<dim>3</dim>
					<dim>480</dim>
					<dim>480</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="onnx::Conv_931_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 3, 5, 5" offset="0" size="2400" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>3</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="onnx::Conv_931" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>3</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_931">
					<dim>16</dim>
					<dim>3</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="/backbone/stem/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>480</dim>
					<dim>480</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>3</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="Reshape_223_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="2400" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="Reshape_223" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="/backbone/stem/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/stem/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="/backbone/stem/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/stem/act/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Reshape_235_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 1, 1, 3, 3" offset="2432" size="288" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="Reshape_235" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/backbone/dark2/dark2.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>240</dim>
					<dim>240</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="Reshape_287_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="2720" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="Reshape_287" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="/backbone/dark2/dark2.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="onnx::Conv_937_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 16, 1, 1" offset="2752" size="1024" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="onnx::Conv_937" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_937">
					<dim>32</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="/backbone/dark2/dark2.0/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="Reshape_303_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="3776" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Reshape_303" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="/backbone/dark2/dark2.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/backbone/dark2/dark2.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="onnx::Conv_940_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 32, 1, 1" offset="3840" size="1024" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="onnx::Conv_940" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_940">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="/backbone/dark2/dark2.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="Reshape_320_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="4864" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="Reshape_320" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/backbone/dark2/dark2.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="/backbone/dark2/dark2.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="onnx::Conv_946_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 16, 1, 1" offset="4896" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="onnx::Conv_946" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_946">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="/backbone/dark2/dark2.1/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="Reshape_354_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="5408" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="Reshape_354" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="/backbone/dark2/dark2.1/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/backbone/dark2/dark2.1/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="Reshape_366_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 1, 1, 3, 3" offset="5440" size="288" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="Reshape_366" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="/backbone/dark2/dark2.1/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="Reshape_418_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="5728" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="Reshape_418" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="/backbone/dark2/dark2.1/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="onnx::Conv_952_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 16, 1, 1" offset="5760" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="onnx::Conv_952" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_952">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="/backbone/dark2/dark2.1/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="Reshape_434_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="6272" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="Reshape_434" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="/backbone/dark2/dark2.1/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="/backbone/dark2/dark2.1/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="/backbone/dark2/dark2.1/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="onnx::Conv_943_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 32, 1, 1" offset="6304" size="1024" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="onnx::Conv_943" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_943">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="/backbone/dark2/dark2.1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="Reshape_337_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="7328" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="Reshape_337" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="/backbone/dark2/dark2.1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="/backbone/dark2/dark2.1/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.1/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/backbone/dark2/dark2.1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/Concat_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="onnx::Conv_955_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="7360" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="onnx::Conv_955" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_955">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="/backbone/dark2/dark2.1/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="Reshape_453_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="9408" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="Reshape_453" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="/backbone/dark2/dark2.1/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark2/dark2.1/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="/backbone/dark2/dark2.1/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark2/dark2.1/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="Reshape_465_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 1, 1, 3, 3" offset="9472" size="576" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="Reshape_465" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="/backbone/dark3/dark3.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>120</dim>
					<dim>120</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="Reshape_517_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="10048" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="Reshape_517" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="/backbone/dark3/dark3.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="onnx::Conv_961_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 32, 1, 1" offset="10112" size="4096" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="onnx::Conv_961" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_961">
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="/backbone/dark3/dark3.0/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="Reshape_533_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="14208" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="Reshape_533" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="/backbone/dark3/dark3.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="/backbone/dark3/dark3.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="onnx::Conv_964_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 64, 1, 1" offset="14336" size="4096" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="onnx::Conv_964" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_964">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="/backbone/dark3/dark3.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="Reshape_550_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="18432" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="Reshape_550" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="/backbone/dark3/dark3.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="/backbone/dark3/dark3.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="onnx::Conv_970_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="18496" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="onnx::Conv_970" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_970">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="/backbone/dark3/dark3.1/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="Reshape_584_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="20544" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="Reshape_584" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="/backbone/dark3/dark3.1/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="/backbone/dark3/dark3.1/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="Reshape_596_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 1, 1, 3, 3" offset="20608" size="576" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="Reshape_596" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="/backbone/dark3/dark3.1/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="Reshape_648_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="21184" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="Reshape_648" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="/backbone/dark3/dark3.1/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="onnx::Conv_976_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="21248" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="onnx::Conv_976" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_976">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="/backbone/dark3/dark3.1/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="Reshape_664_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="23296" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="Reshape_664" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="/backbone/dark3/dark3.1/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/backbone/dark3/dark3.1/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="/backbone/dark3/dark3.1/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="onnx::Conv_979_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="23360" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="onnx::Conv_979" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_979">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="/backbone/dark3/dark3.1/m/m.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="Reshape_682_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="25408" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="Reshape_682" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="/backbone/dark3/dark3.1/m/m.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="/backbone/dark3/dark3.1/m/m.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="Reshape_694_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 1, 1, 3, 3" offset="25472" size="576" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="Reshape_694" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/backbone/dark3/dark3.1/m/m.1/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="Reshape_746_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="26048" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="Reshape_746" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="/backbone/dark3/dark3.1/m/m.1/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="onnx::Conv_985_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="26112" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="onnx::Conv_985" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_985">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="/backbone/dark3/dark3.1/m/m.1/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="Reshape_762_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="28160" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="Reshape_762" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="/backbone/dark3/dark3.1/m/m.1/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="/backbone/dark3/dark3.1/m/m.1/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="/backbone/dark3/dark3.1/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="onnx::Conv_988_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="28224" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="onnx::Conv_988" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_988">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="/backbone/dark3/dark3.1/m/m.2/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="Reshape_780_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="30272" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="Reshape_780" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="/backbone/dark3/dark3.1/m/m.2/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="/backbone/dark3/dark3.1/m/m.2/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="Reshape_792_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 1, 1, 3, 3" offset="30336" size="576" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="Reshape_792" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="/backbone/dark3/dark3.1/m/m.2/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="Reshape_844_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="30912" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="Reshape_844" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="/backbone/dark3/dark3.1/m/m.2/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="onnx::Conv_994_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="30976" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="onnx::Conv_994" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_994">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="/backbone/dark3/dark3.1/m/m.2/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="Reshape_860_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="33024" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="Reshape_860" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="/backbone/dark3/dark3.1/m/m.2/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="/backbone/dark3/dark3.1/m/m.2/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="/backbone/dark3/dark3.1/m/m.2/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/m/m.2/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="onnx::Conv_967_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 64, 1, 1" offset="33088" size="4096" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="onnx::Conv_967" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_967">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="/backbone/dark3/dark3.1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="Reshape_567_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="37184" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="Reshape_567" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="/backbone/dark3/dark3.1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="/backbone/dark3/dark3.1/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="/backbone/dark3/dark3.1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/Concat_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="onnx::Conv_997_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="37248" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="onnx::Conv_997" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_997">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="/backbone/dark3/dark3.1/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="Reshape_879_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="45440" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="Reshape_879" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="/backbone/dark3/dark3.1/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark3/dark3.1/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="/backbone/dark3/dark3.1/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark3/dark3.1/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="Reshape_891_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="45568" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="Reshape_891" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="/backbone/dark4/dark4.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="Reshape_943_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="46720" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="Reshape_943" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="/backbone/dark4/dark4.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="onnx::Conv_1003_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 64, 1, 1" offset="46848" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="onnx::Conv_1003" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1003">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="/backbone/dark4/dark4.0/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="Reshape_959_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="63232" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="Reshape_959" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="/backbone/dark4/dark4.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="/backbone/dark4/dark4.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="onnx::Conv_1006_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="63488" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="onnx::Conv_1006" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1006">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="/backbone/dark4/dark4.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="Reshape_976_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="79872" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="Reshape_976" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="/backbone/dark4/dark4.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="/backbone/dark4/dark4.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="onnx::Conv_1012_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="80000" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="onnx::Conv_1012" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1012">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="/backbone/dark4/dark4.1/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="Reshape_1010_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="88192" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="Reshape_1010" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="/backbone/dark4/dark4.1/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="/backbone/dark4/dark4.1/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="Reshape_1022_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="88320" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="Reshape_1022" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="/backbone/dark4/dark4.1/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="Reshape_1074_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="89472" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="Reshape_1074" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="/backbone/dark4/dark4.1/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="onnx::Conv_1018_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="89600" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="onnx::Conv_1018" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1018">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="/backbone/dark4/dark4.1/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="Reshape_1090_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="97792" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="Reshape_1090" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="/backbone/dark4/dark4.1/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="/backbone/dark4/dark4.1/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="/backbone/dark4/dark4.1/m/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.0/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="onnx::Conv_1021_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="97920" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="onnx::Conv_1021" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1021">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="/backbone/dark4/dark4.1/m/m.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="Reshape_1108_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="106112" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="Reshape_1108" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="/backbone/dark4/dark4.1/m/m.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="/backbone/dark4/dark4.1/m/m.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="Reshape_1120_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="106240" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="Reshape_1120" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="/backbone/dark4/dark4.1/m/m.1/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="Reshape_1172_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="107392" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="Reshape_1172" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="/backbone/dark4/dark4.1/m/m.1/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="onnx::Conv_1027_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="107520" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="onnx::Conv_1027" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1027">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="/backbone/dark4/dark4.1/m/m.1/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="Reshape_1188_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="115712" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="Reshape_1188" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="/backbone/dark4/dark4.1/m/m.1/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="/backbone/dark4/dark4.1/m/m.1/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="/backbone/dark4/dark4.1/m/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.1/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="onnx::Conv_1030_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="115840" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="onnx::Conv_1030" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1030">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="/backbone/dark4/dark4.1/m/m.2/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="Reshape_1206_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="124032" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="Reshape_1206" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="/backbone/dark4/dark4.1/m/m.2/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="/backbone/dark4/dark4.1/m/m.2/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="Reshape_1218_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="124160" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="Reshape_1218" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="/backbone/dark4/dark4.1/m/m.2/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="Reshape_1270_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="125312" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="Reshape_1270" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="/backbone/dark4/dark4.1/m/m.2/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="onnx::Conv_1036_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="125440" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="onnx::Conv_1036" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1036">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="/backbone/dark4/dark4.1/m/m.2/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="Reshape_1286_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="133632" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="Reshape_1286" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="/backbone/dark4/dark4.1/m/m.2/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="/backbone/dark4/dark4.1/m/m.2/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="/backbone/dark4/dark4.1/m/m.2/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/m/m.2/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="onnx::Conv_1009_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="133760" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="onnx::Conv_1009" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1009">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="/backbone/dark4/dark4.1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="Reshape_993_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="150144" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="Reshape_993" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="/backbone/dark4/dark4.1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="/backbone/dark4/dark4.1/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="/backbone/dark4/dark4.1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="onnx::Conv_1039_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="150272" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="onnx::Conv_1039" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1039">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="/backbone/dark4/dark4.1/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="Reshape_1305_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="183040" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="Reshape_1305" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="/backbone/dark4/dark4.1/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark4/dark4.1/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="/backbone/dark4/dark4.1/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark4/dark4.1/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="Reshape_1317_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 3, 3" offset="183296" size="2304" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="Reshape_1317" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="/backbone/dark5/dark5.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="Reshape_1369_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="185600" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="Reshape_1369" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="/backbone/dark5/dark5.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="onnx::Conv_1045_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 128, 1, 1" offset="185856" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="onnx::Conv_1045" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1045">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="/backbone/dark5/dark5.0/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="Reshape_1385_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="251392" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="Reshape_1385" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="/backbone/dark5/dark5.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="/backbone/dark5/dark5.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="onnx::Conv_1048_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="251904" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="onnx::Conv_1048" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1048">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="/backbone/dark5/dark5.1/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="Reshape_1402_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="317440" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="Reshape_1402" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="/backbone/dark5/dark5.1/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.1/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="/backbone/dark5/dark5.1/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.1/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="/backbone/dark5/dark5.1/m.0/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.1/m.0/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="/backbone/dark5/dark5.1/m.1/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="4, 4" pads_end="4, 4" kernel="9, 9" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.1/m.1/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="/backbone/dark5/dark5.1/m.2/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="6, 6" pads_end="6, 6" kernel="13, 13" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.1/m.2/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="/backbone/dark5/dark5.1/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/backbone/dark5/dark5.1/Concat_output_0">
					<dim>1</dim>
					<dim>512</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="onnx::Conv_1051_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 512, 1, 1" offset="317696" size="262144" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="onnx::Conv_1051" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1051">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="/backbone/dark5/dark5.1/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>512</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="Reshape_1423_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="579840" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="Reshape_1423" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="/backbone/dark5/dark5.1/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.1/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="/backbone/dark5/dark5.1/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.1/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="onnx::Conv_1054_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="580352" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="onnx::Conv_1054" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1054">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="/backbone/dark5/dark5.2/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="Reshape_1440_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="645888" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="Reshape_1440" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="/backbone/dark5/dark5.2/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="/backbone/dark5/dark5.2/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.2/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="onnx::Conv_1060_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="646144" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="onnx::Conv_1060" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1060">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="/backbone/dark5/dark5.2/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="Reshape_1474_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="678912" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="Reshape_1474" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="/backbone/dark5/dark5.2/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="/backbone/dark5/dark5.2/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.2/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="Reshape_1486_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 3, 3" offset="679168" size="2304" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="Reshape_1486" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="/backbone/dark5/dark5.2/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="Reshape_1538_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="681472" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="Reshape_1538" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="/backbone/dark5/dark5.2/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="onnx::Conv_1066_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="681728" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="onnx::Conv_1066" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1066">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="/backbone/dark5/dark5.2/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="Reshape_1554_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="714496" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="Reshape_1554" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="/backbone/dark5/dark5.2/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="/backbone/dark5/dark5.2/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.2/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="onnx::Conv_1057_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="714752" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="onnx::Conv_1057" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1057">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="/backbone/dark5/dark5.2/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="Reshape_1457_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="780288" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="Reshape_1457" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="/backbone/dark5/dark5.2/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="/backbone/dark5/dark5.2/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.2/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="/backbone/dark5/dark5.2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="onnx::Conv_1069_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 256, 1, 1" offset="780544" size="131072" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="onnx::Conv_1069" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1069">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="/backbone/dark5/dark5.2/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="Reshape_1572_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="911616" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="Reshape_1572" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="/backbone/dark5/dark5.2/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/backbone/dark5/dark5.2/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="/backbone/dark5/dark5.2/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/backbone/dark5/dark5.2/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="onnx::Conv_1072_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="912128" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="onnx::Conv_1072" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1072">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="/neck/lateral_conv0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="Reshape_1589_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="977664" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="Reshape_1589" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="/neck/lateral_conv0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/lateral_conv0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="/neck/lateral_conv0/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/lateral_conv0/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="/neck/upsample/Constant_output_0" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="977920" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="/neck/upsample/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="/neck/upsample/Resize" type="Interpolate" version="opset11">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/upsample/Resize_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="/neck/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="onnx::Conv_1075_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="977936" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="onnx::Conv_1075" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1075">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="/neck/C3_p4/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="Reshape_1608_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1010704" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="Reshape_1608" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="/neck/C3_p4/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="/neck/C3_p4/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p4/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="onnx::Conv_1081_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1010832" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="onnx::Conv_1081" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1081">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="/neck/C3_p4/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="Reshape_1642_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1019024" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="Reshape_1642" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="/neck/C3_p4/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="/neck/C3_p4/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p4/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="Reshape_1654_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="1019152" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="Reshape_1654" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="/neck/C3_p4/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="Reshape_1706_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1020304" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="Reshape_1706" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="/neck/C3_p4/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="onnx::Conv_1087_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1020432" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="onnx::Conv_1087" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1087">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="/neck/C3_p4/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="Reshape_1722_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1028624" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="Reshape_1722" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="/neck/C3_p4/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="/neck/C3_p4/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p4/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="onnx::Conv_1078_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="1028752" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="onnx::Conv_1078" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1078">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="/neck/C3_p4/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="Reshape_1625_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1061520" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="Reshape_1625" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="/neck/C3_p4/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="/neck/C3_p4/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p4/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="/neck/C3_p4/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="onnx::Conv_1090_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="1061648" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="onnx::Conv_1090" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1090">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="/neck/C3_p4/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="Reshape_1740_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1094416" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="Reshape_1740" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="/neck/C3_p4/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p4/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="/neck/C3_p4/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p4/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="onnx::Conv_1093_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="1094672" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="onnx::Conv_1093" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1093">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="/neck/reduce_conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="Reshape_1757_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1111056" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="Reshape_1757" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="/neck/reduce_conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/reduce_conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="/neck/reduce_conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/reduce_conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="/neck/upsample_1/Resize" type="Interpolate" version="opset11">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/upsample_1/Resize_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="/neck/Concat_1" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_1_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="onnx::Conv_1096_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 128, 1, 1" offset="1111184" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="onnx::Conv_1096" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1096">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="/neck/C3_p3/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="Reshape_1776_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="1119376" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="Reshape_1776" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="/neck/C3_p3/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="/neck/C3_p3/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p3/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="onnx::Conv_1102_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="1119440" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="onnx::Conv_1102" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1102">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="/neck/C3_p3/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="Reshape_1810_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="1121488" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="Reshape_1810" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="/neck/C3_p3/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="407" name="/neck/C3_p3/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p3/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="408" name="Reshape_1822_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 1, 1, 3, 3" offset="1121552" size="576" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="409" name="Reshape_1822" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="410" name="/neck/C3_p3/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="Reshape_1874_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="1122128" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="Reshape_1874" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="/neck/C3_p3/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="onnx::Conv_1108_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="1122192" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="onnx::Conv_1108" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1108">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="416" name="/neck/C3_p3/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="417" name="Reshape_1890_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="1124240" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="418" name="Reshape_1890" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="/neck/C3_p3/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="/neck/C3_p3/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p3/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="onnx::Conv_1099_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 128, 1, 1" offset="1124304" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="onnx::Conv_1099" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1099">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="/neck/C3_p3/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="Reshape_1793_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="1132496" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="Reshape_1793" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="/neck/C3_p3/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="427" name="/neck/C3_p3/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p3/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="428" name="/neck/C3_p3/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/Concat_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="onnx::Conv_1111_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1132560" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="onnx::Conv_1111" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1111">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="/neck/C3_p3/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="Reshape_1908_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1140752" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="Reshape_1908" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="/neck/C3_p3/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_p3/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="/neck/C3_p3/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_p3/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="onnx::Conv_1162_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1140880" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="onnx::Conv_1162" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1162">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="/head/stems.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="Reshape_2385_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1149072" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="Reshape_2385" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="/head/stems.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="/head/stems.0/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.0/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="Reshape_2397_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="1149200" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="Reshape_2397" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="Reshape_2449_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1152400" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="Reshape_2449" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="Reshape_2460_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="1152528" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="Reshape_2460" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="Reshape_2512_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1160720" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="Reshape_2512" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="/head/group_convs.0/group_convs.0.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.0/group_convs.0.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="Reshape_2524_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="1160976" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="Reshape_2524" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="Reshape_2576_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1167376" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="Reshape_2576" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="Reshape_2587_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="1167632" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="Reshape_2587" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="Reshape_2639_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1184016" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="Reshape_2639" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.0/group_convs.0.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="/head/group_convs.0/group_convs.0.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.0/group_convs.0.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="Constant_8113" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184272" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="Constant_8116" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="Constant_8119" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="/head/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="head.reg_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="1184320" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="head.reg_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.0.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="/head/reg_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="Reshape_2674_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="1185600" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="477" name="Reshape_2674" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="/head/reg_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="head.obj_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1185620" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="head.obj_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.0.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="481" name="/head/obj_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="482" name="Reshape_2690_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="1185748" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="483" name="Reshape_2690" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="484" name="/head/obj_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="485" name="/head/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="486" name="Constant_8125" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="487" name="Constant_8128" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1185750" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="488" name="Constant_8131" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="/head/Slice_1" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_1_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="head.cls_preds.0.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="1185766" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="491" name="head.cls_preds.0.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.0.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="492" name="/head/cls_preds.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="493" name="Reshape_2658_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="1186278" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="494" name="Reshape_2658" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="495" name="/head/cls_preds.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.0/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="496" name="/head/Sigmoid_1" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_1_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="497" name="/head/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
			</output>
		</layer>
		<layer id="498" name="442" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="1186286" size="24" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64" names="442">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="/head/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>3600</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="Reshape_1920_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="1186310" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="Reshape_1920" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="/neck/bu_conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>60</dim>
					<dim>60</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="Reshape_1972_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1187462" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="Reshape_1972" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="/neck/bu_conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bu_conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="506" name="onnx::Conv_1117_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1187590" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="507" name="onnx::Conv_1117" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1117">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="508" name="/neck/bu_conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="509" name="Reshape_1988_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1195782" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="510" name="Reshape_1988" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="511" name="/neck/bu_conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bu_conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="512" name="/neck/bu_conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bu_conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="513" name="/neck/Concat_2" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_2_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="onnx::Conv_1120_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="1195910" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="onnx::Conv_1120" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1120">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="/neck/C3_n3/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="Reshape_2006_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1212294" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="Reshape_2006" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="/neck/C3_n3/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="/neck/C3_n3/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n3/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="onnx::Conv_1126_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1212422" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="onnx::Conv_1126" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1126">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="/neck/C3_n3/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="Reshape_2040_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1220614" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="Reshape_2040" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="/neck/C3_n3/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="527" name="/neck/C3_n3/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n3/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="528" name="Reshape_2052_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 3, 3" offset="1220742" size="1152" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="529" name="Reshape_2052" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="530" name="/neck/C3_n3/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="531" name="Reshape_2104_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1221894" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="532" name="Reshape_2104" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="533" name="/neck/C3_n3/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="534" name="onnx::Conv_1132_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="1222022" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="onnx::Conv_1132" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1132">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="/neck/C3_n3/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="Reshape_2120_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1230214" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="Reshape_2120" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="/neck/C3_n3/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="/neck/C3_n3/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n3/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="541" name="onnx::Conv_1123_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="1230342" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="542" name="onnx::Conv_1123" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1123">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="543" name="/neck/C3_n3/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="544" name="Reshape_2023_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1246726" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="545" name="Reshape_2023" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="546" name="/neck/C3_n3/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="547" name="/neck/C3_n3/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n3/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="548" name="/neck/C3_n3/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="549" name="onnx::Conv_1135_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="1246854" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="550" name="onnx::Conv_1135" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1135">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="551" name="/neck/C3_n3/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="552" name="Reshape_2138_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1279622" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="553" name="Reshape_2138" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="554" name="/neck/C3_n3/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n3/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="555" name="/neck/C3_n3/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n3/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="556" name="onnx::Conv_1177_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="1279878" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="557" name="onnx::Conv_1177" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1177">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="558" name="/head/stems.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="559" name="Reshape_2709_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1296262" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="560" name="Reshape_2709" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="561" name="/head/stems.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="562" name="/head/stems.1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.1/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="563" name="Reshape_2721_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="1296390" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="564" name="Reshape_2721" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="565" name="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="566" name="Reshape_2773_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1299590" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="567" name="Reshape_2773" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="568" name="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="569" name="Reshape_2784_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="1299718" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="570" name="Reshape_2784" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="571" name="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="572" name="Reshape_2836_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1307910" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="573" name="Reshape_2836" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="574" name="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="575" name="/head/group_convs.1/group_convs.1.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.1/group_convs.1.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="576" name="Reshape_2848_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="1308166" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="577" name="Reshape_2848" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="578" name="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="579" name="Reshape_2900_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1314566" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="580" name="Reshape_2900" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="581" name="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="582" name="Reshape_2911_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="1314822" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="583" name="Reshape_2911" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="584" name="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="585" name="Reshape_2963_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1331206" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="586" name="Reshape_2963" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="587" name="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.1/group_convs.1.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="588" name="/head/group_convs.1/group_convs.1.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.1/group_convs.1.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="589" name="Constant_8137" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184272" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="590" name="Constant_8140" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="591" name="Constant_8143" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="592" name="/head/Slice_2" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_2_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="593" name="head.reg_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="1331462" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="594" name="head.reg_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.1.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="595" name="/head/reg_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="596" name="Reshape_2998_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="1332742" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="597" name="Reshape_2998" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="598" name="/head/reg_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="599" name="head.obj_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1332762" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="600" name="head.obj_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.1.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="601" name="/head/obj_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="602" name="Reshape_3014_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="1332890" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="603" name="Reshape_3014" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="604" name="/head/obj_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="605" name="/head/Sigmoid_2" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_2_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="606" name="Constant_8149" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="607" name="Constant_8152" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1185750" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="608" name="Constant_8155" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="609" name="/head/Slice_3" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_3_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="610" name="head.cls_preds.1.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="1332892" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="611" name="head.cls_preds.1.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.1.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="612" name="/head/cls_preds.1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="613" name="Reshape_2982_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="1333404" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="614" name="Reshape_2982" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="615" name="/head/cls_preds.1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.1/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="616" name="/head/Sigmoid_3" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_3_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="617" name="/head/Concat_1" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_1_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
			</output>
		</layer>
		<layer id="618" name="/head/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_1_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>900</dim>
				</port>
			</output>
		</layer>
		<layer id="619" name="Reshape_2150_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 3, 3" offset="1333412" size="2304" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="620" name="Reshape_2150" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="621" name="/neck/bu_conv1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="2, 2" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>30</dim>
					<dim>30</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="622" name="Reshape_2202_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1335716" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="623" name="Reshape_2202" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="624" name="/neck/bu_conv1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bu_conv1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="625" name="onnx::Conv_1141_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="1335972" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="626" name="onnx::Conv_1141" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1141">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="627" name="/neck/bu_conv1/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="628" name="Reshape_2218_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1368740" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="629" name="Reshape_2218" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="630" name="/neck/bu_conv1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/bu_conv1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="631" name="/neck/bu_conv1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/bu_conv1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="632" name="/neck/Concat_3" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/Concat_3_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="633" name="onnx::Conv_1144_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="1368996" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="634" name="onnx::Conv_1144" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1144">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="635" name="/neck/C3_n4/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="636" name="Reshape_2236_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1434532" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="637" name="Reshape_2236" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="638" name="/neck/C3_n4/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="639" name="/neck/C3_n4/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n4/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="640" name="onnx::Conv_1150_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="1434788" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="641" name="onnx::Conv_1150" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1150">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="642" name="/neck/C3_n4/m/m.0/conv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="643" name="Reshape_2270_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1467556" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="644" name="Reshape_2270" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="645" name="/neck/C3_n4/m/m.0/conv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/m/m.0/conv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="646" name="/neck/C3_n4/m/m.0/conv1/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n4/m/m.0/conv1/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="647" name="Reshape_2282_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 3, 3" offset="1467812" size="2304" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="648" name="Reshape_2282" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="649" name="/neck/C3_n4/m/m.0/conv2/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="1, 1" pads_end="1, 1" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="650" name="Reshape_2334_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1470116" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="651" name="Reshape_2334" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="652" name="/neck/C3_n4/m/m.0/conv2/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/m/m.0/conv2/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="653" name="onnx::Conv_1156_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="1470372" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="654" name="onnx::Conv_1156" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1156">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="655" name="/neck/C3_n4/m/m.0/conv2/pconv/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="656" name="Reshape_2350_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1503140" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="657" name="Reshape_2350" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="658" name="/neck/C3_n4/m/m.0/conv2/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/m/m.0/conv2/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="659" name="/neck/C3_n4/m/m.0/conv2/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n4/m/m.0/conv2/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="660" name="onnx::Conv_1147_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="1503396" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="661" name="onnx::Conv_1147" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1147">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="662" name="/neck/C3_n4/conv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="663" name="Reshape_2253_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1568932" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="664" name="Reshape_2253" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="665" name="/neck/C3_n4/conv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/conv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="666" name="/neck/C3_n4/conv2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n4/conv2/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="667" name="/neck/C3_n4/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="668" name="onnx::Conv_1159_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 256, 1, 1" offset="1569188" size="131072" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="669" name="onnx::Conv_1159" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1159">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="670" name="/neck/C3_n4/conv3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="671" name="Reshape_2368_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="1700260" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="672" name="Reshape_2368" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="673" name="/neck/C3_n4/conv3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/neck/C3_n4/conv3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="674" name="/neck/C3_n4/conv3/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/neck/C3_n4/conv3/act/Relu_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="675" name="onnx::Conv_1192_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 1, 1" offset="1700772" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="676" name="onnx::Conv_1192" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="onnx::Conv_1192">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="677" name="/head/stems.2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="678" name="Reshape_3033_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1733540" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="679" name="Reshape_3033" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="680" name="/head/stems.2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/stems.2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="681" name="/head/stems.2/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/stems.2/act/Relu_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="682" name="Reshape_3045_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 1, 1, 5, 5" offset="1733668" size="3200" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="683" name="Reshape_3045" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="684" name="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="685" name="Reshape_3097_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1736868" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="686" name="Reshape_3097" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="687" name="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.0/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="688" name="Reshape_3108_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 32, 1, 1" offset="1736996" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="689" name="Reshape_3108" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="690" name="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="691" name="Reshape_3160_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1745188" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="692" name="Reshape_3160" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="693" name="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.0/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="694" name="/head/group_convs.2/group_convs.2.0/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.2/group_convs.2.0/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="695" name="Reshape_3172_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 1, 1, 5, 5" offset="1745444" size="6400" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="696" name="Reshape_3172" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="697" name="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="2, 2" pads_end="2, 2" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="698" name="Reshape_3224_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1751844" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="699" name="Reshape_3224" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="700" name="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.1/dconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="701" name="Reshape_3235_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="2, 64, 64, 1, 1" offset="1752100" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="702" name="Reshape_3235" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="703" name="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv/WithoutBiases" type="GroupConvolution" version="opset1">
			<data strides="1, 1" pads_begin="0, 0" pads_end="0, 0" dilations="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="704" name="Reshape_3287_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1768484" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="705" name="Reshape_3287" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="706" name="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/group_convs.2/group_convs.2.1/pconv/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="707" name="/head/group_convs.2/group_convs.2.1/pconv/act/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/group_convs.2/group_convs.2.1/pconv/act/Relu_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="708" name="Constant_8161" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184272" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="709" name="Constant_8164" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="710" name="Constant_8167" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="711" name="/head/Slice_4" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_4_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="712" name="head.reg_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="10, 64, 1, 1" offset="1768740" size="1280" />
			<output>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="713" name="head.reg_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.reg_preds.2.weight">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="714" name="/head/reg_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>10</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="715" name="Reshape_3322_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 10, 1, 1" offset="1770020" size="20" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="716" name="Reshape_3322" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="717" name="/head/reg_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/reg_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="718" name="head.obj_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="1770040" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="719" name="head.obj_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.obj_preds.2.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="720" name="/head/obj_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="721" name="Reshape_3338_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1, 1" offset="1770168" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="722" name="Reshape_3338" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="723" name="/head/obj_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/obj_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="724" name="/head/Sigmoid_4" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_4_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="725" name="Constant_8173" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184288" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="726" name="Constant_8176" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1185750" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="727" name="Constant_8179" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1184304" size="16" />
			<rt_info>
				<attribute name="precise" version="0" />
			</rt_info>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="728" name="/head/Slice_5" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/head/Slice_5_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="729" name="head.cls_preds.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="1770170" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="730" name="head.cls_preds.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="head.cls_preds.2.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="731" name="/head/cls_preds.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="732" name="Reshape_3306_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="1770682" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="733" name="Reshape_3306" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="734" name="/head/cls_preds.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/cls_preds.2/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="735" name="/head/Sigmoid_5" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/head/Sigmoid_5_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="736" name="/head/Concat_2" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>10</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_2_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="737" name="/head/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>15</dim>
					<dim>15</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/head/Reshape_2_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>225</dim>
				</port>
			</output>
		</layer>
		<layer id="738" name="/head/Concat_6" type="Concat" version="opset1">
			<data axis="2" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>3600</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>900</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>225</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/head/Concat_6_output_0">
					<dim>1</dim>
					<dim>15</dim>
					<dim>4725</dim>
				</port>
			</output>
		</layer>
		<layer id="739" name="Constant_3349" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="1770690" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="740" name="output" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>15</dim>
					<dim>4725</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="output">
					<dim>1</dim>
					<dim>4725</dim>
					<dim>15</dim>
				</port>
			</output>
		</layer>
		<layer id="741" name="output/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4725</dim>
					<dim>15</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="3" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="2" from-port="1" to-layer="3" to-port="1" />
		<edge from-layer="3" from-port="2" to-layer="6" to-port="0" />
		<edge from-layer="4" from-port="0" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="6" to-port="1" />
		<edge from-layer="6" from-port="2" to-layer="7" to-port="0" />
		<edge from-layer="7" from-port="1" to-layer="10" to-port="0" />
		<edge from-layer="8" from-port="0" to-layer="9" to-port="0" />
		<edge from-layer="9" from-port="1" to-layer="10" to-port="1" />
		<edge from-layer="10" from-port="2" to-layer="13" to-port="0" />
		<edge from-layer="11" from-port="0" to-layer="12" to-port="0" />
		<edge from-layer="12" from-port="1" to-layer="13" to-port="1" />
		<edge from-layer="13" from-port="2" to-layer="16" to-port="0" />
		<edge from-layer="14" from-port="0" to-layer="15" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="16" to-port="1" />
		<edge from-layer="16" from-port="2" to-layer="19" to-port="0" />
		<edge from-layer="17" from-port="0" to-layer="18" to-port="0" />
		<edge from-layer="18" from-port="1" to-layer="19" to-port="1" />
		<edge from-layer="19" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="20" from-port="1" to-layer="23" to-port="0" />
		<edge from-layer="20" from-port="1" to-layer="51" to-port="0" />
		<edge from-layer="21" from-port="0" to-layer="22" to-port="0" />
		<edge from-layer="22" from-port="1" to-layer="23" to-port="1" />
		<edge from-layer="23" from-port="2" to-layer="26" to-port="0" />
		<edge from-layer="24" from-port="0" to-layer="25" to-port="0" />
		<edge from-layer="25" from-port="1" to-layer="26" to-port="1" />
		<edge from-layer="26" from-port="2" to-layer="27" to-port="0" />
		<edge from-layer="27" from-port="1" to-layer="30" to-port="0" />
		<edge from-layer="27" from-port="1" to-layer="48" to-port="1" />
		<edge from-layer="28" from-port="0" to-layer="29" to-port="0" />
		<edge from-layer="29" from-port="1" to-layer="30" to-port="1" />
		<edge from-layer="30" from-port="2" to-layer="33" to-port="0" />
		<edge from-layer="31" from-port="0" to-layer="32" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="33" to-port="1" />
		<edge from-layer="33" from-port="2" to-layer="34" to-port="0" />
		<edge from-layer="34" from-port="1" to-layer="37" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="0" />
		<edge from-layer="36" from-port="1" to-layer="37" to-port="1" />
		<edge from-layer="37" from-port="2" to-layer="40" to-port="0" />
		<edge from-layer="38" from-port="0" to-layer="39" to-port="0" />
		<edge from-layer="39" from-port="1" to-layer="40" to-port="1" />
		<edge from-layer="40" from-port="2" to-layer="43" to-port="0" />
		<edge from-layer="41" from-port="0" to-layer="42" to-port="0" />
		<edge from-layer="42" from-port="1" to-layer="43" to-port="1" />
		<edge from-layer="43" from-port="2" to-layer="46" to-port="0" />
		<edge from-layer="44" from-port="0" to-layer="45" to-port="0" />
		<edge from-layer="45" from-port="1" to-layer="46" to-port="1" />
		<edge from-layer="46" from-port="2" to-layer="47" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="48" to-port="0" />
		<edge from-layer="48" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="49" from-port="0" to-layer="50" to-port="0" />
		<edge from-layer="50" from-port="1" to-layer="51" to-port="1" />
		<edge from-layer="51" from-port="2" to-layer="54" to-port="0" />
		<edge from-layer="52" from-port="0" to-layer="53" to-port="0" />
		<edge from-layer="53" from-port="1" to-layer="54" to-port="1" />
		<edge from-layer="54" from-port="2" to-layer="55" to-port="0" />
		<edge from-layer="55" from-port="1" to-layer="56" to-port="1" />
		<edge from-layer="56" from-port="2" to-layer="59" to-port="0" />
		<edge from-layer="57" from-port="0" to-layer="58" to-port="0" />
		<edge from-layer="58" from-port="1" to-layer="59" to-port="1" />
		<edge from-layer="59" from-port="2" to-layer="62" to-port="0" />
		<edge from-layer="60" from-port="0" to-layer="61" to-port="0" />
		<edge from-layer="61" from-port="1" to-layer="62" to-port="1" />
		<edge from-layer="62" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="63" from-port="1" to-layer="66" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="65" to-port="0" />
		<edge from-layer="65" from-port="1" to-layer="66" to-port="1" />
		<edge from-layer="66" from-port="2" to-layer="69" to-port="0" />
		<edge from-layer="67" from-port="0" to-layer="68" to-port="0" />
		<edge from-layer="68" from-port="1" to-layer="69" to-port="1" />
		<edge from-layer="69" from-port="2" to-layer="72" to-port="0" />
		<edge from-layer="70" from-port="0" to-layer="71" to-port="0" />
		<edge from-layer="71" from-port="1" to-layer="72" to-port="1" />
		<edge from-layer="72" from-port="2" to-layer="75" to-port="0" />
		<edge from-layer="73" from-port="0" to-layer="74" to-port="0" />
		<edge from-layer="74" from-port="1" to-layer="75" to-port="1" />
		<edge from-layer="75" from-port="2" to-layer="76" to-port="0" />
		<edge from-layer="76" from-port="1" to-layer="79" to-port="0" />
		<edge from-layer="76" from-port="1" to-layer="149" to-port="0" />
		<edge from-layer="77" from-port="0" to-layer="78" to-port="0" />
		<edge from-layer="78" from-port="1" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="82" to-port="0" />
		<edge from-layer="80" from-port="0" to-layer="81" to-port="0" />
		<edge from-layer="81" from-port="1" to-layer="82" to-port="1" />
		<edge from-layer="82" from-port="2" to-layer="83" to-port="0" />
		<edge from-layer="83" from-port="1" to-layer="104" to-port="1" />
		<edge from-layer="83" from-port="1" to-layer="86" to-port="0" />
		<edge from-layer="84" from-port="0" to-layer="85" to-port="0" />
		<edge from-layer="85" from-port="1" to-layer="86" to-port="1" />
		<edge from-layer="86" from-port="2" to-layer="89" to-port="0" />
		<edge from-layer="87" from-port="0" to-layer="88" to-port="0" />
		<edge from-layer="88" from-port="1" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="2" to-layer="90" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="93" to-port="0" />
		<edge from-layer="91" from-port="0" to-layer="92" to-port="0" />
		<edge from-layer="92" from-port="1" to-layer="93" to-port="1" />
		<edge from-layer="93" from-port="2" to-layer="96" to-port="0" />
		<edge from-layer="94" from-port="0" to-layer="95" to-port="0" />
		<edge from-layer="95" from-port="1" to-layer="96" to-port="1" />
		<edge from-layer="96" from-port="2" to-layer="99" to-port="0" />
		<edge from-layer="97" from-port="0" to-layer="98" to-port="0" />
		<edge from-layer="98" from-port="1" to-layer="99" to-port="1" />
		<edge from-layer="99" from-port="2" to-layer="102" to-port="0" />
		<edge from-layer="100" from-port="0" to-layer="101" to-port="0" />
		<edge from-layer="101" from-port="1" to-layer="102" to-port="1" />
		<edge from-layer="102" from-port="2" to-layer="103" to-port="0" />
		<edge from-layer="103" from-port="1" to-layer="104" to-port="0" />
		<edge from-layer="104" from-port="2" to-layer="125" to-port="1" />
		<edge from-layer="104" from-port="2" to-layer="107" to-port="0" />
		<edge from-layer="105" from-port="0" to-layer="106" to-port="0" />
		<edge from-layer="106" from-port="1" to-layer="107" to-port="1" />
		<edge from-layer="107" from-port="2" to-layer="110" to-port="0" />
		<edge from-layer="108" from-port="0" to-layer="109" to-port="0" />
		<edge from-layer="109" from-port="1" to-layer="110" to-port="1" />
		<edge from-layer="110" from-port="2" to-layer="111" to-port="0" />
		<edge from-layer="111" from-port="1" to-layer="114" to-port="0" />
		<edge from-layer="112" from-port="0" to-layer="113" to-port="0" />
		<edge from-layer="113" from-port="1" to-layer="114" to-port="1" />
		<edge from-layer="114" from-port="2" to-layer="117" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="0" />
		<edge from-layer="116" from-port="1" to-layer="117" to-port="1" />
		<edge from-layer="117" from-port="2" to-layer="120" to-port="0" />
		<edge from-layer="118" from-port="0" to-layer="119" to-port="0" />
		<edge from-layer="119" from-port="1" to-layer="120" to-port="1" />
		<edge from-layer="120" from-port="2" to-layer="123" to-port="0" />
		<edge from-layer="121" from-port="0" to-layer="122" to-port="0" />
		<edge from-layer="122" from-port="1" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="124" to-port="0" />
		<edge from-layer="124" from-port="1" to-layer="125" to-port="0" />
		<edge from-layer="125" from-port="2" to-layer="128" to-port="0" />
		<edge from-layer="125" from-port="2" to-layer="146" to-port="1" />
		<edge from-layer="126" from-port="0" to-layer="127" to-port="0" />
		<edge from-layer="127" from-port="1" to-layer="128" to-port="1" />
		<edge from-layer="128" from-port="2" to-layer="131" to-port="0" />
		<edge from-layer="129" from-port="0" to-layer="130" to-port="0" />
		<edge from-layer="130" from-port="1" to-layer="131" to-port="1" />
		<edge from-layer="131" from-port="2" to-layer="132" to-port="0" />
		<edge from-layer="132" from-port="1" to-layer="135" to-port="0" />
		<edge from-layer="133" from-port="0" to-layer="134" to-port="0" />
		<edge from-layer="134" from-port="1" to-layer="135" to-port="1" />
		<edge from-layer="135" from-port="2" to-layer="138" to-port="0" />
		<edge from-layer="136" from-port="0" to-layer="137" to-port="0" />
		<edge from-layer="137" from-port="1" to-layer="138" to-port="1" />
		<edge from-layer="138" from-port="2" to-layer="141" to-port="0" />
		<edge from-layer="139" from-port="0" to-layer="140" to-port="0" />
		<edge from-layer="140" from-port="1" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="144" to-port="0" />
		<edge from-layer="142" from-port="0" to-layer="143" to-port="0" />
		<edge from-layer="143" from-port="1" to-layer="144" to-port="1" />
		<edge from-layer="144" from-port="2" to-layer="145" to-port="0" />
		<edge from-layer="145" from-port="1" to-layer="146" to-port="0" />
		<edge from-layer="146" from-port="2" to-layer="154" to-port="0" />
		<edge from-layer="147" from-port="0" to-layer="148" to-port="0" />
		<edge from-layer="148" from-port="1" to-layer="149" to-port="1" />
		<edge from-layer="149" from-port="2" to-layer="152" to-port="0" />
		<edge from-layer="150" from-port="0" to-layer="151" to-port="0" />
		<edge from-layer="151" from-port="1" to-layer="152" to-port="1" />
		<edge from-layer="152" from-port="2" to-layer="153" to-port="0" />
		<edge from-layer="153" from-port="1" to-layer="154" to-port="1" />
		<edge from-layer="154" from-port="2" to-layer="157" to-port="0" />
		<edge from-layer="155" from-port="0" to-layer="156" to-port="0" />
		<edge from-layer="156" from-port="1" to-layer="157" to-port="1" />
		<edge from-layer="157" from-port="2" to-layer="160" to-port="0" />
		<edge from-layer="158" from-port="0" to-layer="159" to-port="0" />
		<edge from-layer="159" from-port="1" to-layer="160" to-port="1" />
		<edge from-layer="160" from-port="2" to-layer="161" to-port="0" />
		<edge from-layer="161" from-port="1" to-layer="164" to-port="0" />
		<edge from-layer="161" from-port="1" to-layer="393" to-port="1" />
		<edge from-layer="162" from-port="0" to-layer="163" to-port="0" />
		<edge from-layer="163" from-port="1" to-layer="164" to-port="1" />
		<edge from-layer="164" from-port="2" to-layer="167" to-port="0" />
		<edge from-layer="165" from-port="0" to-layer="166" to-port="0" />
		<edge from-layer="166" from-port="1" to-layer="167" to-port="1" />
		<edge from-layer="167" from-port="2" to-layer="170" to-port="0" />
		<edge from-layer="168" from-port="0" to-layer="169" to-port="0" />
		<edge from-layer="169" from-port="1" to-layer="170" to-port="1" />
		<edge from-layer="170" from-port="2" to-layer="173" to-port="0" />
		<edge from-layer="171" from-port="0" to-layer="172" to-port="0" />
		<edge from-layer="172" from-port="1" to-layer="173" to-port="1" />
		<edge from-layer="173" from-port="2" to-layer="174" to-port="0" />
		<edge from-layer="174" from-port="1" to-layer="177" to-port="0" />
		<edge from-layer="174" from-port="1" to-layer="247" to-port="0" />
		<edge from-layer="175" from-port="0" to-layer="176" to-port="0" />
		<edge from-layer="176" from-port="1" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="180" to-port="0" />
		<edge from-layer="178" from-port="0" to-layer="179" to-port="0" />
		<edge from-layer="179" from-port="1" to-layer="180" to-port="1" />
		<edge from-layer="180" from-port="2" to-layer="181" to-port="0" />
		<edge from-layer="181" from-port="1" to-layer="202" to-port="1" />
		<edge from-layer="181" from-port="1" to-layer="184" to-port="0" />
		<edge from-layer="182" from-port="0" to-layer="183" to-port="0" />
		<edge from-layer="183" from-port="1" to-layer="184" to-port="1" />
		<edge from-layer="184" from-port="2" to-layer="187" to-port="0" />
		<edge from-layer="185" from-port="0" to-layer="186" to-port="0" />
		<edge from-layer="186" from-port="1" to-layer="187" to-port="1" />
		<edge from-layer="187" from-port="2" to-layer="188" to-port="0" />
		<edge from-layer="188" from-port="1" to-layer="191" to-port="0" />
		<edge from-layer="189" from-port="0" to-layer="190" to-port="0" />
		<edge from-layer="190" from-port="1" to-layer="191" to-port="1" />
		<edge from-layer="191" from-port="2" to-layer="194" to-port="0" />
		<edge from-layer="192" from-port="0" to-layer="193" to-port="0" />
		<edge from-layer="193" from-port="1" to-layer="194" to-port="1" />
		<edge from-layer="194" from-port="2" to-layer="197" to-port="0" />
		<edge from-layer="195" from-port="0" to-layer="196" to-port="0" />
		<edge from-layer="196" from-port="1" to-layer="197" to-port="1" />
		<edge from-layer="197" from-port="2" to-layer="200" to-port="0" />
		<edge from-layer="198" from-port="0" to-layer="199" to-port="0" />
		<edge from-layer="199" from-port="1" to-layer="200" to-port="1" />
		<edge from-layer="200" from-port="2" to-layer="201" to-port="0" />
		<edge from-layer="201" from-port="1" to-layer="202" to-port="0" />
		<edge from-layer="202" from-port="2" to-layer="205" to-port="0" />
		<edge from-layer="202" from-port="2" to-layer="223" to-port="1" />
		<edge from-layer="203" from-port="0" to-layer="204" to-port="0" />
		<edge from-layer="204" from-port="1" to-layer="205" to-port="1" />
		<edge from-layer="205" from-port="2" to-layer="208" to-port="0" />
		<edge from-layer="206" from-port="0" to-layer="207" to-port="0" />
		<edge from-layer="207" from-port="1" to-layer="208" to-port="1" />
		<edge from-layer="208" from-port="2" to-layer="209" to-port="0" />
		<edge from-layer="209" from-port="1" to-layer="212" to-port="0" />
		<edge from-layer="210" from-port="0" to-layer="211" to-port="0" />
		<edge from-layer="211" from-port="1" to-layer="212" to-port="1" />
		<edge from-layer="212" from-port="2" to-layer="215" to-port="0" />
		<edge from-layer="213" from-port="0" to-layer="214" to-port="0" />
		<edge from-layer="214" from-port="1" to-layer="215" to-port="1" />
		<edge from-layer="215" from-port="2" to-layer="218" to-port="0" />
		<edge from-layer="216" from-port="0" to-layer="217" to-port="0" />
		<edge from-layer="217" from-port="1" to-layer="218" to-port="1" />
		<edge from-layer="218" from-port="2" to-layer="221" to-port="0" />
		<edge from-layer="219" from-port="0" to-layer="220" to-port="0" />
		<edge from-layer="220" from-port="1" to-layer="221" to-port="1" />
		<edge from-layer="221" from-port="2" to-layer="222" to-port="0" />
		<edge from-layer="222" from-port="1" to-layer="223" to-port="0" />
		<edge from-layer="223" from-port="2" to-layer="244" to-port="1" />
		<edge from-layer="223" from-port="2" to-layer="226" to-port="0" />
		<edge from-layer="224" from-port="0" to-layer="225" to-port="0" />
		<edge from-layer="225" from-port="1" to-layer="226" to-port="1" />
		<edge from-layer="226" from-port="2" to-layer="229" to-port="0" />
		<edge from-layer="227" from-port="0" to-layer="228" to-port="0" />
		<edge from-layer="228" from-port="1" to-layer="229" to-port="1" />
		<edge from-layer="229" from-port="2" to-layer="230" to-port="0" />
		<edge from-layer="230" from-port="1" to-layer="233" to-port="0" />
		<edge from-layer="231" from-port="0" to-layer="232" to-port="0" />
		<edge from-layer="232" from-port="1" to-layer="233" to-port="1" />
		<edge from-layer="233" from-port="2" to-layer="236" to-port="0" />
		<edge from-layer="234" from-port="0" to-layer="235" to-port="0" />
		<edge from-layer="235" from-port="1" to-layer="236" to-port="1" />
		<edge from-layer="236" from-port="2" to-layer="239" to-port="0" />
		<edge from-layer="237" from-port="0" to-layer="238" to-port="0" />
		<edge from-layer="238" from-port="1" to-layer="239" to-port="1" />
		<edge from-layer="239" from-port="2" to-layer="242" to-port="0" />
		<edge from-layer="240" from-port="0" to-layer="241" to-port="0" />
		<edge from-layer="241" from-port="1" to-layer="242" to-port="1" />
		<edge from-layer="242" from-port="2" to-layer="243" to-port="0" />
		<edge from-layer="243" from-port="1" to-layer="244" to-port="0" />
		<edge from-layer="244" from-port="2" to-layer="252" to-port="0" />
		<edge from-layer="245" from-port="0" to-layer="246" to-port="0" />
		<edge from-layer="246" from-port="1" to-layer="247" to-port="1" />
		<edge from-layer="247" from-port="2" to-layer="250" to-port="0" />
		<edge from-layer="248" from-port="0" to-layer="249" to-port="0" />
		<edge from-layer="249" from-port="1" to-layer="250" to-port="1" />
		<edge from-layer="250" from-port="2" to-layer="251" to-port="0" />
		<edge from-layer="251" from-port="1" to-layer="252" to-port="1" />
		<edge from-layer="252" from-port="2" to-layer="255" to-port="0" />
		<edge from-layer="253" from-port="0" to-layer="254" to-port="0" />
		<edge from-layer="254" from-port="1" to-layer="255" to-port="1" />
		<edge from-layer="255" from-port="2" to-layer="258" to-port="0" />
		<edge from-layer="256" from-port="0" to-layer="257" to-port="0" />
		<edge from-layer="257" from-port="1" to-layer="258" to-port="1" />
		<edge from-layer="258" from-port="2" to-layer="259" to-port="0" />
		<edge from-layer="259" from-port="1" to-layer="342" to-port="1" />
		<edge from-layer="259" from-port="1" to-layer="262" to-port="0" />
		<edge from-layer="260" from-port="0" to-layer="261" to-port="0" />
		<edge from-layer="261" from-port="1" to-layer="262" to-port="1" />
		<edge from-layer="262" from-port="2" to-layer="265" to-port="0" />
		<edge from-layer="263" from-port="0" to-layer="264" to-port="0" />
		<edge from-layer="264" from-port="1" to-layer="265" to-port="1" />
		<edge from-layer="265" from-port="2" to-layer="268" to-port="0" />
		<edge from-layer="266" from-port="0" to-layer="267" to-port="0" />
		<edge from-layer="267" from-port="1" to-layer="268" to-port="1" />
		<edge from-layer="268" from-port="2" to-layer="271" to-port="0" />
		<edge from-layer="269" from-port="0" to-layer="270" to-port="0" />
		<edge from-layer="270" from-port="1" to-layer="271" to-port="1" />
		<edge from-layer="271" from-port="2" to-layer="272" to-port="0" />
		<edge from-layer="272" from-port="1" to-layer="275" to-port="0" />
		<edge from-layer="273" from-port="0" to-layer="274" to-port="0" />
		<edge from-layer="274" from-port="1" to-layer="275" to-port="1" />
		<edge from-layer="275" from-port="2" to-layer="278" to-port="0" />
		<edge from-layer="276" from-port="0" to-layer="277" to-port="0" />
		<edge from-layer="277" from-port="1" to-layer="278" to-port="1" />
		<edge from-layer="278" from-port="2" to-layer="279" to-port="0" />
		<edge from-layer="279" from-port="1" to-layer="280" to-port="0" />
		<edge from-layer="279" from-port="1" to-layer="281" to-port="0" />
		<edge from-layer="279" from-port="1" to-layer="282" to-port="0" />
		<edge from-layer="279" from-port="1" to-layer="283" to-port="0" />
		<edge from-layer="280" from-port="1" to-layer="283" to-port="1" />
		<edge from-layer="281" from-port="1" to-layer="283" to-port="2" />
		<edge from-layer="282" from-port="1" to-layer="283" to-port="3" />
		<edge from-layer="283" from-port="4" to-layer="286" to-port="0" />
		<edge from-layer="284" from-port="0" to-layer="285" to-port="0" />
		<edge from-layer="285" from-port="1" to-layer="286" to-port="1" />
		<edge from-layer="286" from-port="2" to-layer="289" to-port="0" />
		<edge from-layer="287" from-port="0" to-layer="288" to-port="0" />
		<edge from-layer="288" from-port="1" to-layer="289" to-port="1" />
		<edge from-layer="289" from-port="2" to-layer="290" to-port="0" />
		<edge from-layer="290" from-port="1" to-layer="320" to-port="0" />
		<edge from-layer="290" from-port="1" to-layer="293" to-port="0" />
		<edge from-layer="291" from-port="0" to-layer="292" to-port="0" />
		<edge from-layer="292" from-port="1" to-layer="293" to-port="1" />
		<edge from-layer="293" from-port="2" to-layer="296" to-port="0" />
		<edge from-layer="294" from-port="0" to-layer="295" to-port="0" />
		<edge from-layer="295" from-port="1" to-layer="296" to-port="1" />
		<edge from-layer="296" from-port="2" to-layer="297" to-port="0" />
		<edge from-layer="297" from-port="1" to-layer="300" to-port="0" />
		<edge from-layer="298" from-port="0" to-layer="299" to-port="0" />
		<edge from-layer="299" from-port="1" to-layer="300" to-port="1" />
		<edge from-layer="300" from-port="2" to-layer="303" to-port="0" />
		<edge from-layer="301" from-port="0" to-layer="302" to-port="0" />
		<edge from-layer="302" from-port="1" to-layer="303" to-port="1" />
		<edge from-layer="303" from-port="2" to-layer="304" to-port="0" />
		<edge from-layer="304" from-port="1" to-layer="307" to-port="0" />
		<edge from-layer="305" from-port="0" to-layer="306" to-port="0" />
		<edge from-layer="306" from-port="1" to-layer="307" to-port="1" />
		<edge from-layer="307" from-port="2" to-layer="310" to-port="0" />
		<edge from-layer="308" from-port="0" to-layer="309" to-port="0" />
		<edge from-layer="309" from-port="1" to-layer="310" to-port="1" />
		<edge from-layer="310" from-port="2" to-layer="313" to-port="0" />
		<edge from-layer="311" from-port="0" to-layer="312" to-port="0" />
		<edge from-layer="312" from-port="1" to-layer="313" to-port="1" />
		<edge from-layer="313" from-port="2" to-layer="316" to-port="0" />
		<edge from-layer="314" from-port="0" to-layer="315" to-port="0" />
		<edge from-layer="315" from-port="1" to-layer="316" to-port="1" />
		<edge from-layer="316" from-port="2" to-layer="317" to-port="0" />
		<edge from-layer="317" from-port="1" to-layer="325" to-port="0" />
		<edge from-layer="318" from-port="0" to-layer="319" to-port="0" />
		<edge from-layer="319" from-port="1" to-layer="320" to-port="1" />
		<edge from-layer="320" from-port="2" to-layer="323" to-port="0" />
		<edge from-layer="321" from-port="0" to-layer="322" to-port="0" />
		<edge from-layer="322" from-port="1" to-layer="323" to-port="1" />
		<edge from-layer="323" from-port="2" to-layer="324" to-port="0" />
		<edge from-layer="324" from-port="1" to-layer="325" to-port="1" />
		<edge from-layer="325" from-port="2" to-layer="328" to-port="0" />
		<edge from-layer="326" from-port="0" to-layer="327" to-port="0" />
		<edge from-layer="327" from-port="1" to-layer="328" to-port="1" />
		<edge from-layer="328" from-port="2" to-layer="331" to-port="0" />
		<edge from-layer="329" from-port="0" to-layer="330" to-port="0" />
		<edge from-layer="330" from-port="1" to-layer="331" to-port="1" />
		<edge from-layer="331" from-port="2" to-layer="332" to-port="0" />
		<edge from-layer="332" from-port="1" to-layer="335" to-port="0" />
		<edge from-layer="333" from-port="0" to-layer="334" to-port="0" />
		<edge from-layer="334" from-port="1" to-layer="335" to-port="1" />
		<edge from-layer="335" from-port="2" to-layer="338" to-port="0" />
		<edge from-layer="336" from-port="0" to-layer="337" to-port="0" />
		<edge from-layer="337" from-port="1" to-layer="338" to-port="1" />
		<edge from-layer="338" from-port="2" to-layer="339" to-port="0" />
		<edge from-layer="339" from-port="1" to-layer="341" to-port="0" />
		<edge from-layer="339" from-port="1" to-layer="632" to-port="1" />
		<edge from-layer="340" from-port="0" to-layer="392" to-port="1" />
		<edge from-layer="340" from-port="0" to-layer="341" to-port="1" />
		<edge from-layer="341" from-port="2" to-layer="342" to-port="0" />
		<edge from-layer="342" from-port="2" to-layer="345" to-port="0" />
		<edge from-layer="342" from-port="2" to-layer="372" to-port="0" />
		<edge from-layer="343" from-port="0" to-layer="344" to-port="0" />
		<edge from-layer="344" from-port="1" to-layer="345" to-port="1" />
		<edge from-layer="345" from-port="2" to-layer="348" to-port="0" />
		<edge from-layer="346" from-port="0" to-layer="347" to-port="0" />
		<edge from-layer="347" from-port="1" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="349" to-port="0" />
		<edge from-layer="349" from-port="1" to-layer="352" to-port="0" />
		<edge from-layer="350" from-port="0" to-layer="351" to-port="0" />
		<edge from-layer="351" from-port="1" to-layer="352" to-port="1" />
		<edge from-layer="352" from-port="2" to-layer="355" to-port="0" />
		<edge from-layer="353" from-port="0" to-layer="354" to-port="0" />
		<edge from-layer="354" from-port="1" to-layer="355" to-port="1" />
		<edge from-layer="355" from-port="2" to-layer="356" to-port="0" />
		<edge from-layer="356" from-port="1" to-layer="359" to-port="0" />
		<edge from-layer="357" from-port="0" to-layer="358" to-port="0" />
		<edge from-layer="358" from-port="1" to-layer="359" to-port="1" />
		<edge from-layer="359" from-port="2" to-layer="362" to-port="0" />
		<edge from-layer="360" from-port="0" to-layer="361" to-port="0" />
		<edge from-layer="361" from-port="1" to-layer="362" to-port="1" />
		<edge from-layer="362" from-port="2" to-layer="365" to-port="0" />
		<edge from-layer="363" from-port="0" to-layer="364" to-port="0" />
		<edge from-layer="364" from-port="1" to-layer="365" to-port="1" />
		<edge from-layer="365" from-port="2" to-layer="368" to-port="0" />
		<edge from-layer="366" from-port="0" to-layer="367" to-port="0" />
		<edge from-layer="367" from-port="1" to-layer="368" to-port="1" />
		<edge from-layer="368" from-port="2" to-layer="369" to-port="0" />
		<edge from-layer="369" from-port="1" to-layer="377" to-port="0" />
		<edge from-layer="370" from-port="0" to-layer="371" to-port="0" />
		<edge from-layer="371" from-port="1" to-layer="372" to-port="1" />
		<edge from-layer="372" from-port="2" to-layer="375" to-port="0" />
		<edge from-layer="373" from-port="0" to-layer="374" to-port="0" />
		<edge from-layer="374" from-port="1" to-layer="375" to-port="1" />
		<edge from-layer="375" from-port="2" to-layer="376" to-port="0" />
		<edge from-layer="376" from-port="1" to-layer="377" to-port="1" />
		<edge from-layer="377" from-port="2" to-layer="380" to-port="0" />
		<edge from-layer="378" from-port="0" to-layer="379" to-port="0" />
		<edge from-layer="379" from-port="1" to-layer="380" to-port="1" />
		<edge from-layer="380" from-port="2" to-layer="383" to-port="0" />
		<edge from-layer="381" from-port="0" to-layer="382" to-port="0" />
		<edge from-layer="382" from-port="1" to-layer="383" to-port="1" />
		<edge from-layer="383" from-port="2" to-layer="384" to-port="0" />
		<edge from-layer="384" from-port="1" to-layer="387" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="386" to-port="0" />
		<edge from-layer="386" from-port="1" to-layer="387" to-port="1" />
		<edge from-layer="387" from-port="2" to-layer="390" to-port="0" />
		<edge from-layer="388" from-port="0" to-layer="389" to-port="0" />
		<edge from-layer="389" from-port="1" to-layer="390" to-port="1" />
		<edge from-layer="390" from-port="2" to-layer="391" to-port="0" />
		<edge from-layer="391" from-port="1" to-layer="513" to-port="1" />
		<edge from-layer="391" from-port="1" to-layer="392" to-port="0" />
		<edge from-layer="392" from-port="2" to-layer="393" to-port="0" />
		<edge from-layer="393" from-port="2" to-layer="396" to-port="0" />
		<edge from-layer="393" from-port="2" to-layer="423" to-port="0" />
		<edge from-layer="394" from-port="0" to-layer="395" to-port="0" />
		<edge from-layer="395" from-port="1" to-layer="396" to-port="1" />
		<edge from-layer="396" from-port="2" to-layer="399" to-port="0" />
		<edge from-layer="397" from-port="0" to-layer="398" to-port="0" />
		<edge from-layer="398" from-port="1" to-layer="399" to-port="1" />
		<edge from-layer="399" from-port="2" to-layer="400" to-port="0" />
		<edge from-layer="400" from-port="1" to-layer="403" to-port="0" />
		<edge from-layer="401" from-port="0" to-layer="402" to-port="0" />
		<edge from-layer="402" from-port="1" to-layer="403" to-port="1" />
		<edge from-layer="403" from-port="2" to-layer="406" to-port="0" />
		<edge from-layer="404" from-port="0" to-layer="405" to-port="0" />
		<edge from-layer="405" from-port="1" to-layer="406" to-port="1" />
		<edge from-layer="406" from-port="2" to-layer="407" to-port="0" />
		<edge from-layer="407" from-port="1" to-layer="410" to-port="0" />
		<edge from-layer="408" from-port="0" to-layer="409" to-port="0" />
		<edge from-layer="409" from-port="1" to-layer="410" to-port="1" />
		<edge from-layer="410" from-port="2" to-layer="413" to-port="0" />
		<edge from-layer="411" from-port="0" to-layer="412" to-port="0" />
		<edge from-layer="412" from-port="1" to-layer="413" to-port="1" />
		<edge from-layer="413" from-port="2" to-layer="416" to-port="0" />
		<edge from-layer="414" from-port="0" to-layer="415" to-port="0" />
		<edge from-layer="415" from-port="1" to-layer="416" to-port="1" />
		<edge from-layer="416" from-port="2" to-layer="419" to-port="0" />
		<edge from-layer="417" from-port="0" to-layer="418" to-port="0" />
		<edge from-layer="418" from-port="1" to-layer="419" to-port="1" />
		<edge from-layer="419" from-port="2" to-layer="420" to-port="0" />
		<edge from-layer="420" from-port="1" to-layer="428" to-port="0" />
		<edge from-layer="421" from-port="0" to-layer="422" to-port="0" />
		<edge from-layer="422" from-port="1" to-layer="423" to-port="1" />
		<edge from-layer="423" from-port="2" to-layer="426" to-port="0" />
		<edge from-layer="424" from-port="0" to-layer="425" to-port="0" />
		<edge from-layer="425" from-port="1" to-layer="426" to-port="1" />
		<edge from-layer="426" from-port="2" to-layer="427" to-port="0" />
		<edge from-layer="427" from-port="1" to-layer="428" to-port="1" />
		<edge from-layer="428" from-port="2" to-layer="431" to-port="0" />
		<edge from-layer="429" from-port="0" to-layer="430" to-port="0" />
		<edge from-layer="430" from-port="1" to-layer="431" to-port="1" />
		<edge from-layer="431" from-port="2" to-layer="434" to-port="0" />
		<edge from-layer="432" from-port="0" to-layer="433" to-port="0" />
		<edge from-layer="433" from-port="1" to-layer="434" to-port="1" />
		<edge from-layer="434" from-port="2" to-layer="435" to-port="0" />
		<edge from-layer="435" from-port="1" to-layer="438" to-port="0" />
		<edge from-layer="435" from-port="1" to-layer="502" to-port="0" />
		<edge from-layer="436" from-port="0" to-layer="437" to-port="0" />
		<edge from-layer="437" from-port="1" to-layer="438" to-port="1" />
		<edge from-layer="438" from-port="2" to-layer="441" to-port="0" />
		<edge from-layer="439" from-port="0" to-layer="440" to-port="0" />
		<edge from-layer="440" from-port="1" to-layer="441" to-port="1" />
		<edge from-layer="441" from-port="2" to-layer="442" to-port="0" />
		<edge from-layer="442" from-port="1" to-layer="445" to-port="0" />
		<edge from-layer="443" from-port="0" to-layer="444" to-port="0" />
		<edge from-layer="444" from-port="1" to-layer="445" to-port="1" />
		<edge from-layer="445" from-port="2" to-layer="448" to-port="0" />
		<edge from-layer="446" from-port="0" to-layer="447" to-port="0" />
		<edge from-layer="447" from-port="1" to-layer="448" to-port="1" />
		<edge from-layer="448" from-port="2" to-layer="451" to-port="0" />
		<edge from-layer="449" from-port="0" to-layer="450" to-port="0" />
		<edge from-layer="450" from-port="1" to-layer="451" to-port="1" />
		<edge from-layer="451" from-port="2" to-layer="454" to-port="0" />
		<edge from-layer="452" from-port="0" to-layer="453" to-port="0" />
		<edge from-layer="453" from-port="1" to-layer="454" to-port="1" />
		<edge from-layer="454" from-port="2" to-layer="455" to-port="0" />
		<edge from-layer="455" from-port="1" to-layer="458" to-port="0" />
		<edge from-layer="456" from-port="0" to-layer="457" to-port="0" />
		<edge from-layer="457" from-port="1" to-layer="458" to-port="1" />
		<edge from-layer="458" from-port="2" to-layer="461" to-port="0" />
		<edge from-layer="459" from-port="0" to-layer="460" to-port="0" />
		<edge from-layer="460" from-port="1" to-layer="461" to-port="1" />
		<edge from-layer="461" from-port="2" to-layer="464" to-port="0" />
		<edge from-layer="462" from-port="0" to-layer="463" to-port="0" />
		<edge from-layer="463" from-port="1" to-layer="464" to-port="1" />
		<edge from-layer="464" from-port="2" to-layer="467" to-port="0" />
		<edge from-layer="465" from-port="0" to-layer="466" to-port="0" />
		<edge from-layer="466" from-port="1" to-layer="467" to-port="1" />
		<edge from-layer="467" from-port="2" to-layer="468" to-port="0" />
		<edge from-layer="468" from-port="1" to-layer="472" to-port="0" />
		<edge from-layer="468" from-port="1" to-layer="489" to-port="0" />
		<edge from-layer="469" from-port="0" to-layer="472" to-port="1" />
		<edge from-layer="470" from-port="0" to-layer="472" to-port="2" />
		<edge from-layer="471" from-port="0" to-layer="472" to-port="3" />
		<edge from-layer="472" from-port="4" to-layer="475" to-port="0" />
		<edge from-layer="472" from-port="4" to-layer="481" to-port="0" />
		<edge from-layer="473" from-port="0" to-layer="474" to-port="0" />
		<edge from-layer="474" from-port="1" to-layer="475" to-port="1" />
		<edge from-layer="475" from-port="2" to-layer="478" to-port="0" />
		<edge from-layer="476" from-port="0" to-layer="477" to-port="0" />
		<edge from-layer="477" from-port="1" to-layer="478" to-port="1" />
		<edge from-layer="478" from-port="2" to-layer="497" to-port="0" />
		<edge from-layer="479" from-port="0" to-layer="480" to-port="0" />
		<edge from-layer="480" from-port="1" to-layer="481" to-port="1" />
		<edge from-layer="481" from-port="2" to-layer="484" to-port="0" />
		<edge from-layer="482" from-port="0" to-layer="483" to-port="0" />
		<edge from-layer="483" from-port="1" to-layer="484" to-port="1" />
		<edge from-layer="484" from-port="2" to-layer="485" to-port="0" />
		<edge from-layer="485" from-port="1" to-layer="497" to-port="1" />
		<edge from-layer="486" from-port="0" to-layer="489" to-port="1" />
		<edge from-layer="487" from-port="0" to-layer="489" to-port="2" />
		<edge from-layer="488" from-port="0" to-layer="489" to-port="3" />
		<edge from-layer="489" from-port="4" to-layer="492" to-port="0" />
		<edge from-layer="490" from-port="0" to-layer="491" to-port="0" />
		<edge from-layer="491" from-port="1" to-layer="492" to-port="1" />
		<edge from-layer="492" from-port="2" to-layer="495" to-port="0" />
		<edge from-layer="493" from-port="0" to-layer="494" to-port="0" />
		<edge from-layer="494" from-port="1" to-layer="495" to-port="1" />
		<edge from-layer="495" from-port="2" to-layer="496" to-port="0" />
		<edge from-layer="496" from-port="1" to-layer="497" to-port="2" />
		<edge from-layer="497" from-port="3" to-layer="499" to-port="0" />
		<edge from-layer="498" from-port="0" to-layer="737" to-port="1" />
		<edge from-layer="498" from-port="0" to-layer="618" to-port="1" />
		<edge from-layer="498" from-port="0" to-layer="499" to-port="1" />
		<edge from-layer="499" from-port="2" to-layer="738" to-port="0" />
		<edge from-layer="500" from-port="0" to-layer="501" to-port="0" />
		<edge from-layer="501" from-port="1" to-layer="502" to-port="1" />
		<edge from-layer="502" from-port="2" to-layer="505" to-port="0" />
		<edge from-layer="503" from-port="0" to-layer="504" to-port="0" />
		<edge from-layer="504" from-port="1" to-layer="505" to-port="1" />
		<edge from-layer="505" from-port="2" to-layer="508" to-port="0" />
		<edge from-layer="506" from-port="0" to-layer="507" to-port="0" />
		<edge from-layer="507" from-port="1" to-layer="508" to-port="1" />
		<edge from-layer="508" from-port="2" to-layer="511" to-port="0" />
		<edge from-layer="509" from-port="0" to-layer="510" to-port="0" />
		<edge from-layer="510" from-port="1" to-layer="511" to-port="1" />
		<edge from-layer="511" from-port="2" to-layer="512" to-port="0" />
		<edge from-layer="512" from-port="1" to-layer="513" to-port="0" />
		<edge from-layer="513" from-port="2" to-layer="543" to-port="0" />
		<edge from-layer="513" from-port="2" to-layer="516" to-port="0" />
		<edge from-layer="514" from-port="0" to-layer="515" to-port="0" />
		<edge from-layer="515" from-port="1" to-layer="516" to-port="1" />
		<edge from-layer="516" from-port="2" to-layer="519" to-port="0" />
		<edge from-layer="517" from-port="0" to-layer="518" to-port="0" />
		<edge from-layer="518" from-port="1" to-layer="519" to-port="1" />
		<edge from-layer="519" from-port="2" to-layer="520" to-port="0" />
		<edge from-layer="520" from-port="1" to-layer="523" to-port="0" />
		<edge from-layer="521" from-port="0" to-layer="522" to-port="0" />
		<edge from-layer="522" from-port="1" to-layer="523" to-port="1" />
		<edge from-layer="523" from-port="2" to-layer="526" to-port="0" />
		<edge from-layer="524" from-port="0" to-layer="525" to-port="0" />
		<edge from-layer="525" from-port="1" to-layer="526" to-port="1" />
		<edge from-layer="526" from-port="2" to-layer="527" to-port="0" />
		<edge from-layer="527" from-port="1" to-layer="530" to-port="0" />
		<edge from-layer="528" from-port="0" to-layer="529" to-port="0" />
		<edge from-layer="529" from-port="1" to-layer="530" to-port="1" />
		<edge from-layer="530" from-port="2" to-layer="533" to-port="0" />
		<edge from-layer="531" from-port="0" to-layer="532" to-port="0" />
		<edge from-layer="532" from-port="1" to-layer="533" to-port="1" />
		<edge from-layer="533" from-port="2" to-layer="536" to-port="0" />
		<edge from-layer="534" from-port="0" to-layer="535" to-port="0" />
		<edge from-layer="535" from-port="1" to-layer="536" to-port="1" />
		<edge from-layer="536" from-port="2" to-layer="539" to-port="0" />
		<edge from-layer="537" from-port="0" to-layer="538" to-port="0" />
		<edge from-layer="538" from-port="1" to-layer="539" to-port="1" />
		<edge from-layer="539" from-port="2" to-layer="540" to-port="0" />
		<edge from-layer="540" from-port="1" to-layer="548" to-port="0" />
		<edge from-layer="541" from-port="0" to-layer="542" to-port="0" />
		<edge from-layer="542" from-port="1" to-layer="543" to-port="1" />
		<edge from-layer="543" from-port="2" to-layer="546" to-port="0" />
		<edge from-layer="544" from-port="0" to-layer="545" to-port="0" />
		<edge from-layer="545" from-port="1" to-layer="546" to-port="1" />
		<edge from-layer="546" from-port="2" to-layer="547" to-port="0" />
		<edge from-layer="547" from-port="1" to-layer="548" to-port="1" />
		<edge from-layer="548" from-port="2" to-layer="551" to-port="0" />
		<edge from-layer="549" from-port="0" to-layer="550" to-port="0" />
		<edge from-layer="550" from-port="1" to-layer="551" to-port="1" />
		<edge from-layer="551" from-port="2" to-layer="554" to-port="0" />
		<edge from-layer="552" from-port="0" to-layer="553" to-port="0" />
		<edge from-layer="553" from-port="1" to-layer="554" to-port="1" />
		<edge from-layer="554" from-port="2" to-layer="555" to-port="0" />
		<edge from-layer="555" from-port="1" to-layer="558" to-port="0" />
		<edge from-layer="555" from-port="1" to-layer="621" to-port="0" />
		<edge from-layer="556" from-port="0" to-layer="557" to-port="0" />
		<edge from-layer="557" from-port="1" to-layer="558" to-port="1" />
		<edge from-layer="558" from-port="2" to-layer="561" to-port="0" />
		<edge from-layer="559" from-port="0" to-layer="560" to-port="0" />
		<edge from-layer="560" from-port="1" to-layer="561" to-port="1" />
		<edge from-layer="561" from-port="2" to-layer="562" to-port="0" />
		<edge from-layer="562" from-port="1" to-layer="565" to-port="0" />
		<edge from-layer="563" from-port="0" to-layer="564" to-port="0" />
		<edge from-layer="564" from-port="1" to-layer="565" to-port="1" />
		<edge from-layer="565" from-port="2" to-layer="568" to-port="0" />
		<edge from-layer="566" from-port="0" to-layer="567" to-port="0" />
		<edge from-layer="567" from-port="1" to-layer="568" to-port="1" />
		<edge from-layer="568" from-port="2" to-layer="571" to-port="0" />
		<edge from-layer="569" from-port="0" to-layer="570" to-port="0" />
		<edge from-layer="570" from-port="1" to-layer="571" to-port="1" />
		<edge from-layer="571" from-port="2" to-layer="574" to-port="0" />
		<edge from-layer="572" from-port="0" to-layer="573" to-port="0" />
		<edge from-layer="573" from-port="1" to-layer="574" to-port="1" />
		<edge from-layer="574" from-port="2" to-layer="575" to-port="0" />
		<edge from-layer="575" from-port="1" to-layer="578" to-port="0" />
		<edge from-layer="576" from-port="0" to-layer="577" to-port="0" />
		<edge from-layer="577" from-port="1" to-layer="578" to-port="1" />
		<edge from-layer="578" from-port="2" to-layer="581" to-port="0" />
		<edge from-layer="579" from-port="0" to-layer="580" to-port="0" />
		<edge from-layer="580" from-port="1" to-layer="581" to-port="1" />
		<edge from-layer="581" from-port="2" to-layer="584" to-port="0" />
		<edge from-layer="582" from-port="0" to-layer="583" to-port="0" />
		<edge from-layer="583" from-port="1" to-layer="584" to-port="1" />
		<edge from-layer="584" from-port="2" to-layer="587" to-port="0" />
		<edge from-layer="585" from-port="0" to-layer="586" to-port="0" />
		<edge from-layer="586" from-port="1" to-layer="587" to-port="1" />
		<edge from-layer="587" from-port="2" to-layer="588" to-port="0" />
		<edge from-layer="588" from-port="1" to-layer="609" to-port="0" />
		<edge from-layer="588" from-port="1" to-layer="592" to-port="0" />
		<edge from-layer="589" from-port="0" to-layer="592" to-port="1" />
		<edge from-layer="590" from-port="0" to-layer="592" to-port="2" />
		<edge from-layer="591" from-port="0" to-layer="592" to-port="3" />
		<edge from-layer="592" from-port="4" to-layer="595" to-port="0" />
		<edge from-layer="592" from-port="4" to-layer="601" to-port="0" />
		<edge from-layer="593" from-port="0" to-layer="594" to-port="0" />
		<edge from-layer="594" from-port="1" to-layer="595" to-port="1" />
		<edge from-layer="595" from-port="2" to-layer="598" to-port="0" />
		<edge from-layer="596" from-port="0" to-layer="597" to-port="0" />
		<edge from-layer="597" from-port="1" to-layer="598" to-port="1" />
		<edge from-layer="598" from-port="2" to-layer="617" to-port="0" />
		<edge from-layer="599" from-port="0" to-layer="600" to-port="0" />
		<edge from-layer="600" from-port="1" to-layer="601" to-port="1" />
		<edge from-layer="601" from-port="2" to-layer="604" to-port="0" />
		<edge from-layer="602" from-port="0" to-layer="603" to-port="0" />
		<edge from-layer="603" from-port="1" to-layer="604" to-port="1" />
		<edge from-layer="604" from-port="2" to-layer="605" to-port="0" />
		<edge from-layer="605" from-port="1" to-layer="617" to-port="1" />
		<edge from-layer="606" from-port="0" to-layer="609" to-port="1" />
		<edge from-layer="607" from-port="0" to-layer="609" to-port="2" />
		<edge from-layer="608" from-port="0" to-layer="609" to-port="3" />
		<edge from-layer="609" from-port="4" to-layer="612" to-port="0" />
		<edge from-layer="610" from-port="0" to-layer="611" to-port="0" />
		<edge from-layer="611" from-port="1" to-layer="612" to-port="1" />
		<edge from-layer="612" from-port="2" to-layer="615" to-port="0" />
		<edge from-layer="613" from-port="0" to-layer="614" to-port="0" />
		<edge from-layer="614" from-port="1" to-layer="615" to-port="1" />
		<edge from-layer="615" from-port="2" to-layer="616" to-port="0" />
		<edge from-layer="616" from-port="1" to-layer="617" to-port="2" />
		<edge from-layer="617" from-port="3" to-layer="618" to-port="0" />
		<edge from-layer="618" from-port="2" to-layer="738" to-port="1" />
		<edge from-layer="619" from-port="0" to-layer="620" to-port="0" />
		<edge from-layer="620" from-port="1" to-layer="621" to-port="1" />
		<edge from-layer="621" from-port="2" to-layer="624" to-port="0" />
		<edge from-layer="622" from-port="0" to-layer="623" to-port="0" />
		<edge from-layer="623" from-port="1" to-layer="624" to-port="1" />
		<edge from-layer="624" from-port="2" to-layer="627" to-port="0" />
		<edge from-layer="625" from-port="0" to-layer="626" to-port="0" />
		<edge from-layer="626" from-port="1" to-layer="627" to-port="1" />
		<edge from-layer="627" from-port="2" to-layer="630" to-port="0" />
		<edge from-layer="628" from-port="0" to-layer="629" to-port="0" />
		<edge from-layer="629" from-port="1" to-layer="630" to-port="1" />
		<edge from-layer="630" from-port="2" to-layer="631" to-port="0" />
		<edge from-layer="631" from-port="1" to-layer="632" to-port="0" />
		<edge from-layer="632" from-port="2" to-layer="662" to-port="0" />
		<edge from-layer="632" from-port="2" to-layer="635" to-port="0" />
		<edge from-layer="633" from-port="0" to-layer="634" to-port="0" />
		<edge from-layer="634" from-port="1" to-layer="635" to-port="1" />
		<edge from-layer="635" from-port="2" to-layer="638" to-port="0" />
		<edge from-layer="636" from-port="0" to-layer="637" to-port="0" />
		<edge from-layer="637" from-port="1" to-layer="638" to-port="1" />
		<edge from-layer="638" from-port="2" to-layer="639" to-port="0" />
		<edge from-layer="639" from-port="1" to-layer="642" to-port="0" />
		<edge from-layer="640" from-port="0" to-layer="641" to-port="0" />
		<edge from-layer="641" from-port="1" to-layer="642" to-port="1" />
		<edge from-layer="642" from-port="2" to-layer="645" to-port="0" />
		<edge from-layer="643" from-port="0" to-layer="644" to-port="0" />
		<edge from-layer="644" from-port="1" to-layer="645" to-port="1" />
		<edge from-layer="645" from-port="2" to-layer="646" to-port="0" />
		<edge from-layer="646" from-port="1" to-layer="649" to-port="0" />
		<edge from-layer="647" from-port="0" to-layer="648" to-port="0" />
		<edge from-layer="648" from-port="1" to-layer="649" to-port="1" />
		<edge from-layer="649" from-port="2" to-layer="652" to-port="0" />
		<edge from-layer="650" from-port="0" to-layer="651" to-port="0" />
		<edge from-layer="651" from-port="1" to-layer="652" to-port="1" />
		<edge from-layer="652" from-port="2" to-layer="655" to-port="0" />
		<edge from-layer="653" from-port="0" to-layer="654" to-port="0" />
		<edge from-layer="654" from-port="1" to-layer="655" to-port="1" />
		<edge from-layer="655" from-port="2" to-layer="658" to-port="0" />
		<edge from-layer="656" from-port="0" to-layer="657" to-port="0" />
		<edge from-layer="657" from-port="1" to-layer="658" to-port="1" />
		<edge from-layer="658" from-port="2" to-layer="659" to-port="0" />
		<edge from-layer="659" from-port="1" to-layer="667" to-port="0" />
		<edge from-layer="660" from-port="0" to-layer="661" to-port="0" />
		<edge from-layer="661" from-port="1" to-layer="662" to-port="1" />
		<edge from-layer="662" from-port="2" to-layer="665" to-port="0" />
		<edge from-layer="663" from-port="0" to-layer="664" to-port="0" />
		<edge from-layer="664" from-port="1" to-layer="665" to-port="1" />
		<edge from-layer="665" from-port="2" to-layer="666" to-port="0" />
		<edge from-layer="666" from-port="1" to-layer="667" to-port="1" />
		<edge from-layer="667" from-port="2" to-layer="670" to-port="0" />
		<edge from-layer="668" from-port="0" to-layer="669" to-port="0" />
		<edge from-layer="669" from-port="1" to-layer="670" to-port="1" />
		<edge from-layer="670" from-port="2" to-layer="673" to-port="0" />
		<edge from-layer="671" from-port="0" to-layer="672" to-port="0" />
		<edge from-layer="672" from-port="1" to-layer="673" to-port="1" />
		<edge from-layer="673" from-port="2" to-layer="674" to-port="0" />
		<edge from-layer="674" from-port="1" to-layer="677" to-port="0" />
		<edge from-layer="675" from-port="0" to-layer="676" to-port="0" />
		<edge from-layer="676" from-port="1" to-layer="677" to-port="1" />
		<edge from-layer="677" from-port="2" to-layer="680" to-port="0" />
		<edge from-layer="678" from-port="0" to-layer="679" to-port="0" />
		<edge from-layer="679" from-port="1" to-layer="680" to-port="1" />
		<edge from-layer="680" from-port="2" to-layer="681" to-port="0" />
		<edge from-layer="681" from-port="1" to-layer="684" to-port="0" />
		<edge from-layer="682" from-port="0" to-layer="683" to-port="0" />
		<edge from-layer="683" from-port="1" to-layer="684" to-port="1" />
		<edge from-layer="684" from-port="2" to-layer="687" to-port="0" />
		<edge from-layer="685" from-port="0" to-layer="686" to-port="0" />
		<edge from-layer="686" from-port="1" to-layer="687" to-port="1" />
		<edge from-layer="687" from-port="2" to-layer="690" to-port="0" />
		<edge from-layer="688" from-port="0" to-layer="689" to-port="0" />
		<edge from-layer="689" from-port="1" to-layer="690" to-port="1" />
		<edge from-layer="690" from-port="2" to-layer="693" to-port="0" />
		<edge from-layer="691" from-port="0" to-layer="692" to-port="0" />
		<edge from-layer="692" from-port="1" to-layer="693" to-port="1" />
		<edge from-layer="693" from-port="2" to-layer="694" to-port="0" />
		<edge from-layer="694" from-port="1" to-layer="697" to-port="0" />
		<edge from-layer="695" from-port="0" to-layer="696" to-port="0" />
		<edge from-layer="696" from-port="1" to-layer="697" to-port="1" />
		<edge from-layer="697" from-port="2" to-layer="700" to-port="0" />
		<edge from-layer="698" from-port="0" to-layer="699" to-port="0" />
		<edge from-layer="699" from-port="1" to-layer="700" to-port="1" />
		<edge from-layer="700" from-port="2" to-layer="703" to-port="0" />
		<edge from-layer="701" from-port="0" to-layer="702" to-port="0" />
		<edge from-layer="702" from-port="1" to-layer="703" to-port="1" />
		<edge from-layer="703" from-port="2" to-layer="706" to-port="0" />
		<edge from-layer="704" from-port="0" to-layer="705" to-port="0" />
		<edge from-layer="705" from-port="1" to-layer="706" to-port="1" />
		<edge from-layer="706" from-port="2" to-layer="707" to-port="0" />
		<edge from-layer="707" from-port="1" to-layer="711" to-port="0" />
		<edge from-layer="707" from-port="1" to-layer="728" to-port="0" />
		<edge from-layer="708" from-port="0" to-layer="711" to-port="1" />
		<edge from-layer="709" from-port="0" to-layer="711" to-port="2" />
		<edge from-layer="710" from-port="0" to-layer="711" to-port="3" />
		<edge from-layer="711" from-port="4" to-layer="714" to-port="0" />
		<edge from-layer="711" from-port="4" to-layer="720" to-port="0" />
		<edge from-layer="712" from-port="0" to-layer="713" to-port="0" />
		<edge from-layer="713" from-port="1" to-layer="714" to-port="1" />
		<edge from-layer="714" from-port="2" to-layer="717" to-port="0" />
		<edge from-layer="715" from-port="0" to-layer="716" to-port="0" />
		<edge from-layer="716" from-port="1" to-layer="717" to-port="1" />
		<edge from-layer="717" from-port="2" to-layer="736" to-port="0" />
		<edge from-layer="718" from-port="0" to-layer="719" to-port="0" />
		<edge from-layer="719" from-port="1" to-layer="720" to-port="1" />
		<edge from-layer="720" from-port="2" to-layer="723" to-port="0" />
		<edge from-layer="721" from-port="0" to-layer="722" to-port="0" />
		<edge from-layer="722" from-port="1" to-layer="723" to-port="1" />
		<edge from-layer="723" from-port="2" to-layer="724" to-port="0" />
		<edge from-layer="724" from-port="1" to-layer="736" to-port="1" />
		<edge from-layer="725" from-port="0" to-layer="728" to-port="1" />
		<edge from-layer="726" from-port="0" to-layer="728" to-port="2" />
		<edge from-layer="727" from-port="0" to-layer="728" to-port="3" />
		<edge from-layer="728" from-port="4" to-layer="731" to-port="0" />
		<edge from-layer="729" from-port="0" to-layer="730" to-port="0" />
		<edge from-layer="730" from-port="1" to-layer="731" to-port="1" />
		<edge from-layer="731" from-port="2" to-layer="734" to-port="0" />
		<edge from-layer="732" from-port="0" to-layer="733" to-port="0" />
		<edge from-layer="733" from-port="1" to-layer="734" to-port="1" />
		<edge from-layer="734" from-port="2" to-layer="735" to-port="0" />
		<edge from-layer="735" from-port="1" to-layer="736" to-port="2" />
		<edge from-layer="736" from-port="3" to-layer="737" to-port="0" />
		<edge from-layer="737" from-port="2" to-layer="738" to-port="2" />
		<edge from-layer="738" from-port="3" to-layer="740" to-port="0" />
		<edge from-layer="739" from-port="0" to-layer="740" to-port="1" />
		<edge from-layer="740" from-port="2" to-layer="741" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2024.0.0-14509-34caeefd078-releases/2024/0" />
		<Runtime_version value="2024.0.0-14509-34caeefd078-releases/2024/0" />
		<conversion_parameters>
			<input_model value="DIR/yolox_rune_3.6m.onnx" />
			<is_python_api_used value="False" />
		</conversion_parameters>
		<legacy_frontend value="False" />
	</rt_info>
</net>
