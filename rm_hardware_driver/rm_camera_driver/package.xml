<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rm_camera_driver</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">zcf</maintainer>
  <license>TODO: License declaration</license>

  <!-- buildtool_depend: dependencies of the build process -->
  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- depend: build, export, and execution dependency -->
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>sensor_msgs</depend>
  <depend>image_transport</depend>
  <depend>image_transport_plugins</depend>
  <depend>rm_utils</depend>
  <depend>camera_info_manager</depend>

  <exec_depend>camera_calibration</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
