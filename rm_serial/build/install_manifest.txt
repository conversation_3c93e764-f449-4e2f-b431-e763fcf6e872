/usr/local/share/serial/environment/library_path.sh
/usr/local/share/serial/environment/library_path.dsv
/usr/local/lib/libserial.so
/usr/local/include/serial/serial.h
/usr/local/include/serial/v8stdint.h
/usr/local/share/ament_index/resource_index/package_run_dependencies/serial
/usr/local/share/ament_index/resource_index/parent_prefix_path/serial
/usr/local/share/serial/environment/ament_prefix_path.sh
/usr/local/share/serial/environment/ament_prefix_path.dsv
/usr/local/share/serial/environment/path.sh
/usr/local/share/serial/environment/path.dsv
/usr/local/share/serial/local_setup.bash
/usr/local/share/serial/local_setup.sh
/usr/local/share/serial/local_setup.zsh
/usr/local/share/serial/local_setup.dsv
/usr/local/share/serial/package.dsv
/usr/local/share/ament_index/resource_index/packages/serial
/usr/local/share/serial/cmake/ament_cmake_export_include_directories-extras.cmake
/usr/local/share/serial/cmake/ament_cmake_export_libraries-extras.cmake
/usr/local/share/serial/cmake/serialConfig.cmake
/usr/local/share/serial/cmake/serialConfig-version.cmake
/usr/local/share/serial/package.xml