[0.060s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.061s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffffa6d27460>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffa6e60910>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffa6e60910>>)
[0.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.138s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/serial/build'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5) by extension 'python_setup_py'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'ros'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC) by extension 'python_setup_py'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'ros'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdC/tmp) by extension 'python_setup_py'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'ros'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX) by extension 'python_setup_py'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'ros'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'cmake'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'python'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/4.0.0-rc5/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'ros'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['cmake', 'python']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'cmake'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'python'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extensions ['python_setup_py']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/pkgRedirects) by extension 'python_setup_py'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'ros'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['cmake', 'python']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'cmake'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'python'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extensions ['python_setup_py']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir) by extension 'python_setup_py'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'ros'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['cmake', 'python']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'cmake'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'python'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extensions ['python_setup_py']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src) by extension 'python_setup_py'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'ignore_ament_install'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['colcon_pkg']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'colcon_pkg'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['colcon_meta']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'colcon_meta'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['ros']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'ros'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['cmake', 'python']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'cmake'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'python'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extensions ['python_setup_py']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl) by extension 'python_setup_py'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['ignore', 'ignore_ament_install']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'ignore'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'ignore_ament_install'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['colcon_pkg']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'colcon_pkg'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['colcon_meta']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'colcon_meta'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['ros']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'ros'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['cmake', 'python']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'cmake'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'python'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extensions ['python_setup_py']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial.dir/src/impl/list_ports) by extension 'python_setup_py'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['ignore', 'ignore_ament_install']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'ignore'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'ignore_ament_install'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['colcon_pkg']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'colcon_pkg'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['colcon_meta']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'colcon_meta'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['ros']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'ros'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['cmake', 'python']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'cmake'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'python'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extensions ['python_setup_py']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/serial_uninstall.dir) by extension 'python_setup_py'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['ignore', 'ignore_ament_install']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'ignore'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'ignore_ament_install'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['colcon_pkg']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'colcon_pkg'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['colcon_meta']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'colcon_meta'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['ros']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'ros'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['cmake', 'python']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'cmake'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'python'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extensions ['python_setup_py']
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(CMakeFiles/uninstall.dir) by extension 'python_setup_py'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'ros'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['cmake', 'python']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'cmake'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'python'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extensions ['python_setup_py']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core) by extension 'python_setup_py'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'ros'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['cmake', 'python']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'cmake'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'python'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extensions ['python_setup_py']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_core/stamps) by extension 'python_setup_py'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'ros'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['cmake', 'python']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'cmake'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'python'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extensions ['python_setup_py']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_environment_hooks) by extension 'python_setup_py'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'ros'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['cmake', 'python']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'cmake'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_include_directories) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_export_libraries) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/package_run_dependencies) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/packages) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'ros'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['cmake', 'python']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'cmake'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'python'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extensions ['python_setup_py']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_index/share/ament_index/resource_index/parent_prefix_path) by extension 'python_setup_py'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['colcon_pkg']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'colcon_pkg'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['colcon_meta']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'colcon_meta'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['ros']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'ros'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['cmake', 'python']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'cmake'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'python'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extensions ['python_setup_py']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_package_templates) by extension 'python_setup_py'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['colcon_pkg']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'colcon_pkg'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['colcon_meta']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'colcon_meta'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['ros']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'ros'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['cmake', 'python']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'cmake'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'python'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extensions ['python_setup_py']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(ament_cmake_uninstall_target) by extension 'python_setup_py'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.161s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.161s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.162s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.189s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.190s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.190s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.190s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.190s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.190s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.193s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.193s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.193s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
