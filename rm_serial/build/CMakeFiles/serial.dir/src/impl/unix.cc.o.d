CMakeFiles/serial.dir/src/impl/unix.cc.o: \
 /home/<USER>/serial/src/impl/unix.cc /usr/include/stdc-predef.h \
 /usr/include/stdio.h \
 /usr/include/aarch64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/aarch64-linux-gnu/bits/wordsize.h \
 /usr/include/aarch64-linux-gnu/bits/timesize.h \
 /usr/include/aarch64-linux-gnu/sys/cdefs.h \
 /usr/include/aarch64-linux-gnu/bits/long-double.h \
 /usr/include/aarch64-linux-gnu/gnu/stubs.h \
 /usr/include/aarch64-linux-gnu/gnu/stubs-lp64.h \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/stddef.h \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/stdarg.h \
 /usr/include/aarch64-linux-gnu/bits/types.h \
 /usr/include/aarch64-linux-gnu/bits/typesizes.h \
 /usr/include/aarch64-linux-gnu/bits/time64.h \
 /usr/include/aarch64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__FILE.h \
 /usr/include/aarch64-linux-gnu/bits/types/FILE.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/aarch64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/aarch64-linux-gnu/bits/stdio_lim.h \
 /usr/include/aarch64-linux-gnu/bits/floatn.h \
 /usr/include/aarch64-linux-gnu/bits/floatn-common.h \
 /usr/include/string.h \
 /usr/include/aarch64-linux-gnu/bits/types/locale_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/strings.h /usr/include/c++/11/sstream \
 /usr/include/c++/11/istream /usr/include/c++/11/ios \
 /usr/include/c++/11/iosfwd \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++config.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/os_defines.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/cpu_defines.h \
 /usr/include/c++/11/pstl/pstl_config.h \
 /usr/include/c++/11/bits/stringfwd.h \
 /usr/include/c++/11/bits/memoryfwd.h /usr/include/c++/11/bits/postypes.h \
 /usr/include/c++/11/cwchar /usr/include/wchar.h \
 /usr/include/aarch64-linux-gnu/bits/wchar.h \
 /usr/include/aarch64-linux-gnu/bits/types/wint_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/c++/11/exception /usr/include/c++/11/bits/exception.h \
 /usr/include/c++/11/bits/exception_ptr.h \
 /usr/include/c++/11/bits/exception_defines.h \
 /usr/include/c++/11/bits/cxxabi_init_exception.h \
 /usr/include/c++/11/typeinfo /usr/include/c++/11/bits/hash_bytes.h \
 /usr/include/c++/11/new /usr/include/c++/11/bits/move.h \
 /usr/include/c++/11/type_traits \
 /usr/include/c++/11/bits/nested_exception.h \
 /usr/include/c++/11/bits/char_traits.h \
 /usr/include/c++/11/bits/stl_algobase.h \
 /usr/include/c++/11/bits/functexcept.h \
 /usr/include/c++/11/bits/cpp_type_traits.h \
 /usr/include/c++/11/ext/type_traits.h \
 /usr/include/c++/11/ext/numeric_traits.h \
 /usr/include/c++/11/bits/stl_pair.h \
 /usr/include/c++/11/bits/stl_iterator_base_types.h \
 /usr/include/c++/11/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/11/bits/concept_check.h \
 /usr/include/c++/11/debug/assertions.h \
 /usr/include/c++/11/bits/stl_iterator.h \
 /usr/include/c++/11/bits/ptr_traits.h /usr/include/c++/11/debug/debug.h \
 /usr/include/c++/11/bits/predefined_ops.h /usr/include/c++/11/cstdint \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/stdint.h /usr/include/stdint.h \
 /usr/include/aarch64-linux-gnu/bits/stdint-intn.h \
 /usr/include/aarch64-linux-gnu/bits/stdint-uintn.h \
 /usr/include/c++/11/bits/localefwd.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++locale.h \
 /usr/include/c++/11/clocale /usr/include/locale.h \
 /usr/include/aarch64-linux-gnu/bits/locale.h /usr/include/c++/11/cctype \
 /usr/include/ctype.h /usr/include/aarch64-linux-gnu/bits/endian.h \
 /usr/include/aarch64-linux-gnu/bits/endianness.h \
 /usr/include/c++/11/bits/ios_base.h /usr/include/c++/11/ext/atomicity.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/gthr.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/aarch64-linux-gnu/bits/types/time_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/aarch64-linux-gnu/bits/sched.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/aarch64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/aarch64-linux-gnu/bits/time.h \
 /usr/include/aarch64-linux-gnu/bits/timex.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/aarch64-linux-gnu/bits/types/clock_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/aarch64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/timer_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/aarch64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/aarch64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/aarch64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/aarch64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/aarch64-linux-gnu/bits/struct_mutex.h \
 /usr/include/aarch64-linux-gnu/bits/struct_rwlock.h \
 /usr/include/aarch64-linux-gnu/bits/setjmp.h \
 /usr/include/aarch64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
 /usr/include/aarch64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/atomic_word.h \
 /usr/include/aarch64-linux-gnu/sys/single_threaded.h \
 /usr/include/c++/11/bits/locale_classes.h /usr/include/c++/11/string \
 /usr/include/c++/11/bits/allocator.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++allocator.h \
 /usr/include/c++/11/ext/new_allocator.h \
 /usr/include/c++/11/bits/ostream_insert.h \
 /usr/include/c++/11/bits/cxxabi_forced.h \
 /usr/include/c++/11/bits/stl_function.h \
 /usr/include/c++/11/backward/binders.h \
 /usr/include/c++/11/bits/range_access.h \
 /usr/include/c++/11/initializer_list \
 /usr/include/c++/11/bits/basic_string.h \
 /usr/include/c++/11/ext/alloc_traits.h \
 /usr/include/c++/11/bits/alloc_traits.h \
 /usr/include/c++/11/bits/stl_construct.h /usr/include/c++/11/string_view \
 /usr/include/c++/11/bits/functional_hash.h \
 /usr/include/c++/11/bits/string_view.tcc \
 /usr/include/c++/11/ext/string_conversions.h /usr/include/c++/11/cstdlib \
 /usr/include/stdlib.h /usr/include/aarch64-linux-gnu/bits/waitflags.h \
 /usr/include/aarch64-linux-gnu/bits/waitstatus.h \
 /usr/include/aarch64-linux-gnu/sys/types.h /usr/include/endian.h \
 /usr/include/aarch64-linux-gnu/bits/byteswap.h \
 /usr/include/aarch64-linux-gnu/bits/uintn-identity.h \
 /usr/include/aarch64-linux-gnu/sys/select.h \
 /usr/include/aarch64-linux-gnu/bits/select.h \
 /usr/include/aarch64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/alloca.h /usr/include/aarch64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/11/bits/std_abs.h /usr/include/c++/11/cstdio \
 /usr/include/c++/11/cerrno /usr/include/errno.h \
 /usr/include/aarch64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/aarch64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/aarch64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/11/bits/charconv.h \
 /usr/include/c++/11/bits/basic_string.tcc \
 /usr/include/c++/11/bits/locale_classes.tcc \
 /usr/include/c++/11/system_error \
 /usr/include/aarch64-linux-gnu/c++/11/bits/error_constants.h \
 /usr/include/c++/11/stdexcept /usr/include/c++/11/streambuf \
 /usr/include/c++/11/bits/streambuf.tcc \
 /usr/include/c++/11/bits/basic_ios.h \
 /usr/include/c++/11/bits/locale_facets.h /usr/include/c++/11/cwctype \
 /usr/include/wctype.h /usr/include/aarch64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/ctype_base.h \
 /usr/include/c++/11/bits/streambuf_iterator.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/ctype_inline.h \
 /usr/include/c++/11/bits/locale_facets.tcc \
 /usr/include/c++/11/bits/basic_ios.tcc /usr/include/c++/11/ostream \
 /usr/include/c++/11/bits/ostream.tcc \
 /usr/include/c++/11/bits/istream.tcc \
 /usr/include/c++/11/bits/sstream.tcc /usr/include/unistd.h \
 /usr/include/aarch64-linux-gnu/bits/posix_opt.h \
 /usr/include/aarch64-linux-gnu/bits/environments.h \
 /usr/include/aarch64-linux-gnu/bits/confname.h \
 /usr/include/aarch64-linux-gnu/bits/getopt_posix.h \
 /usr/include/aarch64-linux-gnu/bits/getopt_core.h \
 /usr/include/aarch64-linux-gnu/bits/unistd_ext.h \
 /usr/include/linux/close_range.h /usr/include/fcntl.h \
 /usr/include/aarch64-linux-gnu/bits/fcntl.h \
 /usr/include/aarch64-linux-gnu/bits/fcntl-linux.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_iovec.h \
 /usr/include/linux/falloc.h /usr/include/aarch64-linux-gnu/bits/stat.h \
 /usr/include/aarch64-linux-gnu/bits/struct_stat.h \
 /usr/include/aarch64-linux-gnu/sys/ioctl.h \
 /usr/include/aarch64-linux-gnu/bits/ioctls.h \
 /usr/include/aarch64-linux-gnu/asm/ioctls.h \
 /usr/include/asm-generic/ioctls.h /usr/include/linux/ioctl.h \
 /usr/include/aarch64-linux-gnu/asm/ioctl.h \
 /usr/include/asm-generic/ioctl.h \
 /usr/include/aarch64-linux-gnu/bits/ioctl-types.h \
 /usr/include/aarch64-linux-gnu/sys/ttydefaults.h \
 /usr/include/aarch64-linux-gnu/sys/signal.h /usr/include/signal.h \
 /usr/include/aarch64-linux-gnu/bits/signum-generic.h \
 /usr/include/aarch64-linux-gnu/bits/signum-arch.h \
 /usr/include/aarch64-linux-gnu/bits/types/sig_atomic_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/siginfo_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__sigval_t.h \
 /usr/include/aarch64-linux-gnu/bits/siginfo-arch.h \
 /usr/include/aarch64-linux-gnu/bits/siginfo-consts.h \
 /usr/include/aarch64-linux-gnu/bits/siginfo-consts-arch.h \
 /usr/include/aarch64-linux-gnu/bits/types/sigval_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/sigevent_t.h \
 /usr/include/aarch64-linux-gnu/bits/sigevent-consts.h \
 /usr/include/aarch64-linux-gnu/bits/sigaction.h \
 /usr/include/aarch64-linux-gnu/bits/sigcontext.h \
 /usr/include/aarch64-linux-gnu/asm/sigcontext.h \
 /usr/include/linux/types.h /usr/include/aarch64-linux-gnu/asm/types.h \
 /usr/include/asm-generic/types.h /usr/include/asm-generic/int-ll64.h \
 /usr/include/aarch64-linux-gnu/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h \
 /usr/include/aarch64-linux-gnu/asm/posix_types.h \
 /usr/include/asm-generic/posix_types.h \
 /usr/include/aarch64-linux-gnu/asm/sve_context.h \
 /usr/include/aarch64-linux-gnu/bits/types/stack_t.h \
 /usr/include/aarch64-linux-gnu/sys/ucontext.h \
 /usr/include/aarch64-linux-gnu/sys/procfs.h \
 /usr/include/aarch64-linux-gnu/sys/time.h \
 /usr/include/aarch64-linux-gnu/sys/user.h \
 /usr/include/aarch64-linux-gnu/bits/procfs.h \
 /usr/include/aarch64-linux-gnu/bits/procfs-id.h \
 /usr/include/aarch64-linux-gnu/bits/procfs-prregset.h \
 /usr/include/aarch64-linux-gnu/bits/procfs-extra.h \
 /usr/include/aarch64-linux-gnu/bits/sigstack.h \
 /usr/include/aarch64-linux-gnu/bits/sigstksz.h \
 /usr/include/aarch64-linux-gnu/bits/ss_flags.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_sigstack.h \
 /usr/include/aarch64-linux-gnu/bits/sigthread.h \
 /usr/include/aarch64-linux-gnu/bits/signal_ext.h /usr/include/paths.h \
 /usr/include/sysexits.h /usr/include/termios.h \
 /usr/include/aarch64-linux-gnu/bits/termios.h \
 /usr/include/aarch64-linux-gnu/bits/termios-struct.h \
 /usr/include/aarch64-linux-gnu/bits/termios-c_cc.h \
 /usr/include/aarch64-linux-gnu/bits/termios-c_iflag.h \
 /usr/include/aarch64-linux-gnu/bits/termios-c_oflag.h \
 /usr/include/aarch64-linux-gnu/bits/termios-baud.h \
 /usr/include/aarch64-linux-gnu/bits/termios-c_cflag.h \
 /usr/include/aarch64-linux-gnu/bits/termios-c_lflag.h \
 /usr/include/aarch64-linux-gnu/bits/termios-tcflow.h \
 /usr/include/aarch64-linux-gnu/bits/termios-misc.h \
 /usr/include/aarch64-linux-gnu/sys/param.h \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/limits.h \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/syslimits.h \
 /usr/include/limits.h /usr/include/aarch64-linux-gnu/bits/posix1_lim.h \
 /usr/include/aarch64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/aarch64-linux-gnu/bits/posix2_lim.h \
 /usr/include/aarch64-linux-gnu/bits/xopen_lim.h \
 /usr/include/aarch64-linux-gnu/bits/uio_lim.h \
 /usr/include/aarch64-linux-gnu/bits/param.h /usr/include/linux/param.h \
 /usr/include/aarch64-linux-gnu/asm/param.h \
 /usr/include/asm-generic/param.h /usr/include/linux/serial.h \
 /usr/include/linux/tty_flags.h \
 /home/<USER>/serial/include/serial/impl/unix.h \
 /home/<USER>/serial/include/serial/serial.h /usr/include/c++/11/limits \
 /usr/include/c++/11/vector /usr/include/c++/11/bits/stl_uninitialized.h \
 /usr/include/c++/11/bits/stl_vector.h \
 /usr/include/c++/11/bits/stl_bvector.h \
 /usr/include/c++/11/bits/vector.tcc /usr/include/c++/11/cstring \
 /home/<USER>/serial/include/serial/v8stdint.h
