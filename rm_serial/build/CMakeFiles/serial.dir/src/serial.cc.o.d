CMakeFiles/serial.dir/src/serial.cc.o: /home/<USER>/serial/src/serial.cc \
 /usr/include/stdc-predef.h /usr/include/c++/11/algorithm \
 /usr/include/c++/11/utility \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++config.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/os_defines.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/aarch64-linux-gnu/bits/wordsize.h \
 /usr/include/aarch64-linux-gnu/bits/timesize.h \
 /usr/include/aarch64-linux-gnu/sys/cdefs.h \
 /usr/include/aarch64-linux-gnu/bits/long-double.h \
 /usr/include/aarch64-linux-gnu/gnu/stubs.h \
 /usr/include/aarch64-linux-gnu/gnu/stubs-lp64.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/cpu_defines.h \
 /usr/include/c++/11/pstl/pstl_config.h \
 /usr/include/c++/11/bits/stl_relops.h \
 /usr/include/c++/11/bits/stl_pair.h /usr/include/c++/11/bits/move.h \
 /usr/include/c++/11/type_traits /usr/include/c++/11/initializer_list \
 /usr/include/c++/11/bits/stl_algobase.h \
 /usr/include/c++/11/bits/functexcept.h \
 /usr/include/c++/11/bits/exception_defines.h \
 /usr/include/c++/11/bits/cpp_type_traits.h \
 /usr/include/c++/11/ext/type_traits.h \
 /usr/include/c++/11/ext/numeric_traits.h \
 /usr/include/c++/11/bits/stl_iterator_base_types.h \
 /usr/include/c++/11/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/11/bits/concept_check.h \
 /usr/include/c++/11/debug/assertions.h \
 /usr/include/c++/11/bits/stl_iterator.h \
 /usr/include/c++/11/bits/ptr_traits.h /usr/include/c++/11/debug/debug.h \
 /usr/include/c++/11/bits/predefined_ops.h \
 /usr/include/c++/11/bits/stl_algo.h /usr/include/c++/11/cstdlib \
 /usr/include/stdlib.h \
 /usr/include/aarch64-linux-gnu/bits/libc-header-start.h \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/stddef.h \
 /usr/include/aarch64-linux-gnu/bits/waitflags.h \
 /usr/include/aarch64-linux-gnu/bits/waitstatus.h \
 /usr/include/aarch64-linux-gnu/bits/floatn.h \
 /usr/include/aarch64-linux-gnu/bits/floatn-common.h \
 /usr/include/aarch64-linux-gnu/bits/types/locale_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/aarch64-linux-gnu/sys/types.h \
 /usr/include/aarch64-linux-gnu/bits/types.h \
 /usr/include/aarch64-linux-gnu/bits/typesizes.h \
 /usr/include/aarch64-linux-gnu/bits/time64.h \
 /usr/include/aarch64-linux-gnu/bits/types/clock_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/time_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/timer_t.h \
 /usr/include/aarch64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/aarch64-linux-gnu/bits/endian.h \
 /usr/include/aarch64-linux-gnu/bits/endianness.h \
 /usr/include/aarch64-linux-gnu/bits/byteswap.h \
 /usr/include/aarch64-linux-gnu/bits/uintn-identity.h \
 /usr/include/aarch64-linux-gnu/sys/select.h \
 /usr/include/aarch64-linux-gnu/bits/select.h \
 /usr/include/aarch64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/aarch64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/aarch64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/aarch64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/aarch64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/aarch64-linux-gnu/bits/struct_mutex.h \
 /usr/include/aarch64-linux-gnu/bits/struct_rwlock.h \
 /usr/include/alloca.h /usr/include/aarch64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/11/bits/std_abs.h \
 /usr/include/c++/11/bits/algorithmfwd.h \
 /usr/include/c++/11/bits/stl_heap.h \
 /usr/include/c++/11/bits/stl_tempbuf.h \
 /usr/include/c++/11/bits/stl_construct.h /usr/include/c++/11/new \
 /usr/include/c++/11/bits/exception.h \
 /usr/include/c++/11/bits/uniform_int_dist.h \
 /usr/include/c++/11/pstl/glue_algorithm_defs.h \
 /usr/include/c++/11/functional /usr/include/c++/11/bits/stl_function.h \
 /usr/include/c++/11/backward/binders.h /usr/include/c++/11/tuple \
 /usr/include/c++/11/array /usr/include/c++/11/bits/range_access.h \
 /usr/include/c++/11/bits/uses_allocator.h \
 /usr/include/c++/11/bits/invoke.h \
 /usr/include/c++/11/bits/functional_hash.h \
 /usr/include/c++/11/bits/hash_bytes.h /usr/include/c++/11/bits/refwrap.h \
 /usr/include/c++/11/bits/std_function.h /usr/include/c++/11/typeinfo \
 /usr/include/c++/11/unordered_map /usr/include/c++/11/bits/allocator.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++allocator.h \
 /usr/include/c++/11/ext/new_allocator.h \
 /usr/include/c++/11/bits/memoryfwd.h \
 /usr/include/c++/11/ext/alloc_traits.h \
 /usr/include/c++/11/bits/alloc_traits.h \
 /usr/include/c++/11/ext/aligned_buffer.h \
 /usr/include/c++/11/bits/hashtable.h \
 /usr/include/c++/11/bits/hashtable_policy.h \
 /usr/include/c++/11/bits/enable_special_members.h \
 /usr/include/c++/11/bits/node_handle.h \
 /usr/include/c++/11/bits/unordered_map.h \
 /usr/include/c++/11/bits/erase_if.h /usr/include/c++/11/vector \
 /usr/include/c++/11/bits/stl_uninitialized.h \
 /usr/include/c++/11/bits/stl_vector.h \
 /usr/include/c++/11/bits/stl_bvector.h \
 /usr/include/c++/11/bits/vector.tcc \
 /usr/include/c++/11/pstl/execution_defs.h \
 /home/<USER>/serial/include/serial/serial.h /usr/include/c++/11/limits \
 /usr/include/c++/11/string /usr/include/c++/11/bits/stringfwd.h \
 /usr/include/c++/11/bits/char_traits.h \
 /usr/include/c++/11/bits/postypes.h /usr/include/c++/11/cwchar \
 /usr/include/wchar.h /usr/lib/gcc/aarch64-linux-gnu/11/include/stdarg.h \
 /usr/include/aarch64-linux-gnu/bits/wchar.h \
 /usr/include/aarch64-linux-gnu/bits/types/wint_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__FILE.h \
 /usr/include/aarch64-linux-gnu/bits/types/FILE.h \
 /usr/include/c++/11/cstdint \
 /usr/lib/gcc/aarch64-linux-gnu/11/include/stdint.h /usr/include/stdint.h \
 /usr/include/aarch64-linux-gnu/bits/stdint-uintn.h \
 /usr/include/c++/11/bits/localefwd.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/c++locale.h \
 /usr/include/c++/11/clocale /usr/include/locale.h \
 /usr/include/aarch64-linux-gnu/bits/locale.h /usr/include/c++/11/iosfwd \
 /usr/include/c++/11/cctype /usr/include/ctype.h \
 /usr/include/c++/11/bits/ostream_insert.h \
 /usr/include/c++/11/bits/cxxabi_forced.h \
 /usr/include/c++/11/bits/basic_string.h \
 /usr/include/c++/11/ext/atomicity.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/gthr.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/aarch64-linux-gnu/bits/sched.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/aarch64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/aarch64-linux-gnu/bits/time.h \
 /usr/include/aarch64-linux-gnu/bits/timex.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/aarch64-linux-gnu/bits/setjmp.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
 /usr/include/aarch64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/atomic_word.h \
 /usr/include/aarch64-linux-gnu/sys/single_threaded.h \
 /usr/include/c++/11/string_view /usr/include/c++/11/bits/string_view.tcc \
 /usr/include/c++/11/ext/string_conversions.h /usr/include/c++/11/cstdio \
 /usr/include/stdio.h \
 /usr/include/aarch64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/aarch64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/aarch64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/aarch64-linux-gnu/bits/stdio_lim.h \
 /usr/include/c++/11/cerrno /usr/include/errno.h \
 /usr/include/aarch64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/aarch64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/aarch64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/11/bits/charconv.h \
 /usr/include/c++/11/bits/basic_string.tcc /usr/include/c++/11/cstring \
 /usr/include/string.h /usr/include/strings.h /usr/include/c++/11/sstream \
 /usr/include/c++/11/istream /usr/include/c++/11/ios \
 /usr/include/c++/11/exception /usr/include/c++/11/bits/exception_ptr.h \
 /usr/include/c++/11/bits/cxxabi_init_exception.h \
 /usr/include/c++/11/bits/nested_exception.h \
 /usr/include/c++/11/bits/ios_base.h \
 /usr/include/c++/11/bits/locale_classes.h \
 /usr/include/c++/11/bits/locale_classes.tcc \
 /usr/include/c++/11/system_error \
 /usr/include/aarch64-linux-gnu/c++/11/bits/error_constants.h \
 /usr/include/c++/11/stdexcept /usr/include/c++/11/streambuf \
 /usr/include/c++/11/bits/streambuf.tcc \
 /usr/include/c++/11/bits/basic_ios.h \
 /usr/include/c++/11/bits/locale_facets.h /usr/include/c++/11/cwctype \
 /usr/include/wctype.h /usr/include/aarch64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/ctype_base.h \
 /usr/include/c++/11/bits/streambuf_iterator.h \
 /usr/include/aarch64-linux-gnu/c++/11/bits/ctype_inline.h \
 /usr/include/c++/11/bits/locale_facets.tcc \
 /usr/include/c++/11/bits/basic_ios.tcc /usr/include/c++/11/ostream \
 /usr/include/c++/11/bits/ostream.tcc \
 /usr/include/c++/11/bits/istream.tcc \
 /usr/include/c++/11/bits/sstream.tcc \
 /home/<USER>/serial/include/serial/v8stdint.h \
 /home/<USER>/serial/include/serial/impl/unix.h
