
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/serial/src/impl/list_ports/list_ports_linux.cc" "CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.o" "gcc" "CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.o.d"
  "/home/<USER>/serial/src/impl/unix.cc" "CMakeFiles/serial.dir/src/impl/unix.cc.o" "gcc" "CMakeFiles/serial.dir/src/impl/unix.cc.o.d"
  "/home/<USER>/serial/src/serial.cc" "CMakeFiles/serial.dir/src/serial.cc.o" "gcc" "CMakeFiles/serial.dir/src/serial.cc.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
