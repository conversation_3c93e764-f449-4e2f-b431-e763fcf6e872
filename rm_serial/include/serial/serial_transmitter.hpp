#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"
#include "serial/serial.h"

class DefaultTransmitter{
public:

    DefaultTransmitter(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node) {
        serial_setting(port, baudrate);
    }

    void getSubscription_default(){
        node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
            });
    }

    void getSubscription_infantry(){
        node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
            });
    }

    void getSubscription_sentry(){
        node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
            });
    }

    void send(const rm_interfaces::msg::GimbalCmd &data) {


        uint8_t buffer[16];
        memset(buffer, 0, sizeof(buffer));

        buffer[0] = 0xFF;

        buffer[1] = msg_.fire_advice ? 0x01 : 0x00;
        memcpy(&buffer[2], &msg_.pitch, sizeof(float));
        memcpy(&buffer[6], &msg_.yaw, sizeof(float));
        memcpy(&buffer[10], &msg_.distance, sizeof(float));

        uint8_t checksum = 0;
        for (size_t i = 1; i < 14; i++) {
            checksum ^= buffer[i];
        }
        buffer[14] = checksum;

        buffer[15] = 0x0D;
    }


private:
  typedef struct {
    bool fire_advice;
    float pitch;
    float yaw;
    float distance;
  } MSG;

  MSG msg_;
  rclcpp::Node::SharedPtr node_;
  serial::Serial serial_port;

    void serial_setting(const std::string& port = "/dev/ttyUSB0", int baudrate = 115200){
      try {
          serial_port.setPort(port);
          serial_port.setBaudrate(baudrate);
          serial::Timeout to = serial::Timeout::simpleTimeout(1000);
          serial_port.setTimeout(to);
          serial_port.open();
      } catch (const std::exception &e) {
          RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", e.what());
      }

      if (serial_port.isOpen()) {
          RCLCPP_INFO(node_->get_logger(), "Serial port initialized: %s at %d baud", port.c_str(), baudrate);
      } else {
          RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", port.c_str());
      }
  }

};