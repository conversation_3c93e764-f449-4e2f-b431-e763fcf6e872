#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"

class DefaultTransmitter{
public:

    DefaultTransmitter(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node) {
        
        node->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
                
            });
    }


private:
  typedef struct {
    bool fire_advice;
    float pitch;
    float yaw;
    float distance;
  } MSG;

  MSG msg_;
  rclcpp::Node::SharedPtr node_;
};