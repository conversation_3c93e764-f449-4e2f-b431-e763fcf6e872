#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"
#include "usart.hpp"

class DefaultTransmitter{
public:

    DefaultTransmitter(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node) {
        
        // 初始化串口
        if (!usart_.init(port, baudrate)) {
            RCLCPP_ERROR(node->get_logger(), "Failed to initialize serial port: %s", port.c_str());
            return;
        }
        
        node->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
                
                sendData();
            });
    }


private:
  typedef struct {
    bool fire_advice;
    float pitch;
    float yaw;
    float distance;
  } MSG;

  MSG msg_;
  usart_transmitter usart_;
  rclcpp::Node::SharedPtr node_;
};