#pragma once

#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"

class DefaultTransmitter{
public:

    DefaultTransmitter(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node) {
        
        // 初始化串口
        if (!usart_.init(port, baudrate)) {
            RCLCPP_ERROR(node->get_logger(), "Failed to initialize serial port: %s", port.c_str());
            return;
        }
        
        RCLCPP_INFO(node->get_logger(), "Serial port %s initialized successfully at %d baud", port.c_str(), baudrate);
    }
    
    void getSubscription_default() {
        subscription_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
            "gimbal/cmd", rclcpp::SensorDataQoS(),
            [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) {
                msg_.fire_advice = msg->fire_advice;
                msg_.pitch = msg->pitch;
                msg_.yaw = msg->yaw;
                msg_.distance = msg->distance;
                
                // 发送数据
                sendData();
            });
    }

private:

  typedef struct {
    bool fire_advice;
    float pitch;
    float yaw;
    float distance;
  } MSG;

  rclcpp::Node::SharedPtr node_;
  MSG msg_;
  rclcpp::Subscription<rm_interfaces::msg::GimbalCmd>::SharedPtr subscription_;
  
  // 发送数据
  void sendData() {
    if (!usart_.isInitialized()) {
      RCLCPP_WARN(node_->get_logger(), "USART not initialized, cannot send data");
      return;
    }
    
    if (!usart_.sendPacket(msg_)) {
      RCLCPP_ERROR(node_->get_logger(), "Failed to send data via USART");
    } else {
      RCLCPP_DEBUG(node_->get_logger(), "Data sent: fire=%d, pitch=%.2f, yaw=%.2f, distance=%.2f", 
                   msg_.fire_advice, msg_.pitch, msg_.yaw, msg_.distance);
    }
  }
};
