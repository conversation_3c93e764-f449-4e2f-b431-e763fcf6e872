#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/chassis_cmd.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"
#include "serial/serial.h"

class DefaultTransmitter {
public:
  DefaultTransmitter(const rclcpp::Node::SharedPtr &node,
                     const std::string &port = "/dev/ttyUSB0",
                     int baudrate = 115200)
  : node_(node) {
    serial_setting(port, baudrate);
  }

  void getSubscription_default() {
    gimbal_sub_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
      "armor_solver/cmd_gimbal",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) { this->send(*msg); });
  }

  void getSubscription_infantry() {
    gimbal_sub_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
      "armor_solver/cmd_gimbal",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) { this->send(*msg); });

    rune_gimbal_sub_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
      "rune_solver/cmd_gimbal",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) { this->send(*msg); });
  }

  void getSubscription_sentry() {
    gimbal_sub_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
      "armor_solver/cmd_gimbal",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) { this->send(*msg); });

    rune_gimbal_sub_ = node_->create_subscription<rm_interfaces::msg::GimbalCmd>(
      "rune_solver/cmd_gimbal",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::GimbalCmd::SharedPtr msg) { this->send(*msg); });

    chassis_sub_ = node_->create_subscription<rm_interfaces::msg::ChassisCmd>(
      "/cmd_chassis",
      rclcpp::SensorDataQoS(),
      [this](const rm_interfaces::msg::ChassisCmd::SharedPtr msg) { this->sendChassis(*msg); });
  }

private:
  rclcpp::Node::SharedPtr node_;
  serial::Serial serial_port;

  rclcpp::Subscription<rm_interfaces::msg::GimbalCmd>::SharedPtr gimbal_sub_;
  rclcpp::Subscription<rm_interfaces::msg::GimbalCmd>::SharedPtr rune_gimbal_sub_;
  rclcpp::Subscription<rm_interfaces::msg::ChassisCmd>::SharedPtr chassis_sub_;

  void serial_setting(const std::string &port = "/dev/ttyUSB0", int baudrate = 115200) {
    try {
      serial_port.setPort(port);
      serial_port.setBaudrate(baudrate);
      serial::Timeout to = serial::Timeout::simpleTimeout(1000);
      serial_port.setTimeout(to);
      serial_port.open();
    } catch (const std::exception &e) {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", e.what());
    }

    if (serial_port.isOpen()) {
      RCLCPP_INFO(
        node_->get_logger(), "Serial port initialized: %s at %d baud", port.c_str(), baudrate);
    } else {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", port.c_str());
    }
  }

  void send(const rm_interfaces::msg::GimbalCmd &data) {
    uint8_t buffer[16];
    memset(buffer, 0, sizeof(buffer));

    buffer[0] = 0xFF;

    buffer[1] = data.fire_advice ? 0x01 : 0x00;
    memcpy(&buffer[2], &data.pitch, sizeof(float));
    memcpy(&buffer[6], &data.yaw, sizeof(float));
    memcpy(&buffer[10], &data.distance, sizeof(float));

    uint8_t checksum = 0;
    for (size_t i = 1; i < 14; i++) {
      checksum ^= buffer[i];
    }
    buffer[14] = checksum;

    buffer[15] = 0x0D;

    if (serial_port.isOpen()) {
      serial_port.write(buffer, sizeof(buffer));
    }
  }

  void sendChassis(const rm_interfaces::msg::ChassisCmd &data) {
    uint8_t buffer[32];
    memset(buffer, 0, sizeof(buffer));

    buffer[0] = 0xFF;

    buffer[2] = data.is_spining ? 0x01 : 0x00;
    buffer[3] = data.is_navigating ? 0x01 : 0x00;

    memcpy(&buffer[16], &data.twist.linear.x, sizeof(float));
    memcpy(&buffer[20], &data.twist.linear.y, sizeof(float));
    memcpy(&buffer[24], &data.twist.angular.z, sizeof(float));

    uint8_t checksum = 0;
    for (size_t i = 1; i < 30; i++) {
      checksum ^= buffer[i];
    }
    buffer[30] = checksum;

    buffer[31] = 0x0D;

    if (serial_port.isOpen()) {
      serial_port.write(buffer, sizeof(buffer));
    }
  }
};
