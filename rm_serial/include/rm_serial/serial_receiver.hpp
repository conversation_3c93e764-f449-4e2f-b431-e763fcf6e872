#include "serial/serial.h"
#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/chassis_cmd.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"
#include "rm_interfaces/msg/serial_receive_data.hpp"
#include "rm_interfaces/msg/judge_system_data.hpp"
#include "rm_interfaces/msg/operator_command.hpp"
#include "tf2_ros/transform_broadcaster.h"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include <thread>
#include <atomic>
#include <cstring>

class serialReceiver {
public:
    serialReceiver(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node), is_running_(false) {
        serial_setting(port, baudrate);

        receive_data_pub_ = node_->create_publisher<rm_interfaces::msg::SerialReceiveData>(
            "serial/receive", rclcpp::SensorDataQoS());

        tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(node_);

        memset(recv_buffer_, 0, sizeof(recv_buffer_));
        recv_buf_len_ = 0;
    }

    ~serialReceiver() {
        stopReceiving();
    }

    void startReceiving_infantry() {
        if (is_running_) return;
        is_running_ = true;
        receive_thread_ = std::thread(&serialReceiver::receiveLoop_infantry, this);
    }

    void startReceiving_default() {
        if (is_running_) return;
        is_running_ = true;
        receive_thread_ = std::thread(&serialReceiver::receiveLoop_default, this);
    }

    void startReceiving_sentry() {
        if (is_running_) return;
        is_running_ = true;
        receive_thread_ = std::thread(&serialReceiver::receiveLoop_sentry, this);
    }

    void stopReceiving() {
        is_running_ = false;
        if (receive_thread_.joinable()) {
            receive_thread_.join();
        }
    }

private:
  rclcpp::Node::SharedPtr node_;
  serial::Serial serial_port;

  std::thread receive_thread_;
  std::atomic<bool> is_running_;

  rclcpp::Publisher<rm_interfaces::msg::SerialReceiveData>::SharedPtr receive_data_pub_;
  std::shared_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

  uint8_t recv_buffer_[64];
  int recv_buf_len_;

  void serial_setting(const std::string &port = "/dev/ttyUSB0", int baudrate = 115200) {
    try {
      serial_port.setPort(port);
      serial_port.setBaudrate(baudrate);
      serial::Timeout to = serial::Timeout::simpleTimeout(100);
      serial_port.setTimeout(to);
      serial_port.open();
    } catch (const std::exception &e) {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", e.what());
    }
    if (serial_port.isOpen()) {
      RCLCPP_INFO(
        node_->get_logger(), "Serial port initialized: %s at %d baud", port.c_str(), baudrate);
    } else {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", port.c_str());
    }
  }

  void receiveLoop_infantry() {
    rm_interfaces::msg::SerialReceiveData receive_data;
    uint8_t buffer[16];

    while (is_running_ && rclcpp::ok()) {
      if (!serial_port.isOpen()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        continue;
      }

      try {
        size_t bytes_read = serial_port.read(buffer, sizeof(buffer));
        if (bytes_read == 16 && checkPacket_16(buffer)) {
          parseInfantryData(buffer, receive_data);
          publishData(receive_data);
        }
      } catch (const std::exception &e) {
        RCLCPP_WARN(node_->get_logger(), "Serial read error: %s", e.what());
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
      }
    }
  }

  void receiveLoop_default() {
    receiveLoop_infantry();
  }

  void receiveLoop_sentry() {
    rm_interfaces::msg::SerialReceiveData receive_data;
    uint8_t buffer[32];

    while (is_running_ && rclcpp::ok()) {
      if (!serial_port.isOpen()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        continue;
      }

      try {
        size_t bytes_read = serial_port.read(buffer, sizeof(buffer));
        if (bytes_read == 32 && checkPacket_32(buffer)) {
          parseSentryData(buffer, receive_data);
          publishData(receive_data);
        }
      } catch (const std::exception &e) {
        RCLCPP_WARN(node_->get_logger(), "Serial read error: %s", e.what());
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
      }
    }
  }

  bool checkPacket_16(uint8_t *buffer) {
    if (buffer[0] != 0xFF || buffer[15] != 0x0D) {
      return false;
    }

    uint8_t checksum = 0;
    for (int i = 1; i < 14; i++) {
      checksum ^= buffer[i];
    }
    return checksum == buffer[14];
  }

  bool checkPacket_32(uint8_t *buffer) {
    if (buffer[0] != 0xFF || buffer[31] != 0x0D) {
      return false;
    }

    uint8_t checksum = 0;
    for (int i = 1; i < 30; i++) {
      checksum ^= buffer[i];
    }
    return checksum == buffer[30];
  }

  void parseInfantryData(uint8_t *buffer, rm_interfaces::msg::SerialReceiveData &data) {
    data.mode = buffer[1];

    memcpy(&data.roll, &buffer[2], sizeof(float));
    memcpy(&data.pitch, &buffer[6], sizeof(float));
    memcpy(&data.yaw, &buffer[10], sizeof(float));

    data.bullet_speed = 25.0f;

    data.judge_system_data.blood = 0;
    data.judge_system_data.remaining_time = 0;
    data.judge_system_data.outpost_hp = 0;
    data.judge_system_data.game_status = 0;
  }

  void parseSentryData(uint8_t *buffer, rm_interfaces::msg::SerialReceiveData &data) {
    uint8_t enemy_color = buffer[1];
    data.mode = (enemy_color == 1 ? 1 : 0);

    memcpy(&data.pitch, &buffer[2], sizeof(float));
    memcpy(&data.yaw, &buffer[6], sizeof(float));
    data.roll = 0.0f;

    memcpy(&data.judge_system_data.blood, &buffer[14], sizeof(int16_t));
    memcpy(&data.judge_system_data.remaining_time, &buffer[16], sizeof(int16_t));
    memcpy(&data.judge_system_data.outpost_hp, &buffer[20], sizeof(int16_t));

    data.judge_system_data.operator_command.is_outpost_attacking = buffer[22];
    data.judge_system_data.operator_command.is_retreating = buffer[23];
    data.judge_system_data.operator_command.is_drone_avoiding = buffer[24];

    data.judge_system_data.game_status = buffer[25];

    data.bullet_speed = 25.0f;
  }

  void publishData(const rm_interfaces::msg::SerialReceiveData &data) {
    rm_interfaces::msg::SerialReceiveData pub_data = data;
    pub_data.header.stamp = node_->now();
    pub_data.header.frame_id = "odom";

    receive_data_pub_->publish(pub_data);

    geometry_msgs::msg::TransformStamped t;
    t.header.stamp = node_->now();
    t.header.frame_id = "odom";
    t.child_frame_id = "gimbal_link";

    auto roll = data.roll * M_PI / 180.0;
    auto pitch = -data.pitch * M_PI / 180.0;
    auto yaw = data.yaw * M_PI / 180.0;

    tf2::Quaternion q;
    q.setRPY(roll, pitch, yaw);
    t.transform.rotation = tf2::toMsg(q);

    tf_broadcaster_->sendTransform(t);

    RCLCPP_DEBUG(node_->get_logger(),
        "Received: mode=%d, roll=%.2f, pitch=%.2f, yaw=%.2f, blood=%d",
        data.mode, data.roll, data.pitch, data.yaw, data.judge_system_data.blood);
  }
};