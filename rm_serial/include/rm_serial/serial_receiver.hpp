#include "serial/serial.h"
#include "rclcpp/rclcpp.hpp"
#include "rm_interfaces/msg/chassis_cmd.hpp"
#include "rm_interfaces/msg/gimbal_cmd.hpp"

class serialReceiver {
public:
    serialReceiver(const rclcpp::Node::SharedPtr &node, const std::string& port = "/dev/ttyUSB0", int baudrate = 115200)
    : node_(node) {
        serial_setting(port, baudrate); 
    }

private:
  rclcpp::Node::SharedPtr node_;
  serial::Serial serial_port;

  rclcpp::Subscription<rm_interfaces::msg::GimbalCmd>::SharedPtr gimbal_sub_;
  rclcpp::Subscription<rm_interfaces::msg::GimbalCmd>::SharedPtr rune_gimbal_sub_;
  rclcpp::Subscription<rm_interfaces::msg::ChassisCmd>::SharedPtr chassis_sub_;

  void serial_setting(const std::string &port = "/dev/ttyUSB0", int baudrate = 115200) {
    try {
      serial_port.setPort(port);
      serial_port.setBaudrate(baudrate);
      serial::Timeout to = serial::Timeout::simpleTimeout(1000);
      serial_port.setTimeout(to);
      serial_port.open();
    } catch (const std::exception &e) {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", e.what());
    }
    if (serial_port.isOpen()) {
      RCLCPP_INFO(
        node_->get_logger(), "Serial port initialized: %s at %d baud", port.c_str(), baudrate);
    } else {
      RCLCPP_ERROR(node_->get_logger(), "Failed to open serial port: %s", port.c_str());
    }
  }
};