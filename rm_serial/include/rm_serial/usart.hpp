#pragma once

#include <fcntl.h>
#include <termios.h>
#include <unistd.h>
#include <cstring>
#include <string>
#include <cstdint>

class usart_transmitter {
public:
    usart_transmitter() = default;
    
    ~usart_transmitter() {
        if (serial_fd_ >= 0) {
            close(serial_fd_);
        }
    }
    
    // 初始化串口
    bool init(const std::string& port, int baudrate = 115200) {
        serial_fd_ = open(port.c_str(), O_RDWR | O_NOCTTY | O_SYNC);
        if (serial_fd_ < 0) {
            return false;
        }
        
        struct termios tty;
        if (tcgetattr(serial_fd_, &tty) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }
        
        // 设置波特率
        speed_t speed;
        switch (baudrate) {
            case 9600: speed = B9600; break;
            case 19200: speed = B19200; break;
            case 38400: speed = B38400; break;
            case 57600: speed = B57600; break;
            case 115200: speed = B115200; break;
            case 230400: speed = B230400; break;
            case 460800: speed = B460800; break;
            case 921600: speed = B921600; break;
            default: speed = B115200; break;
        }
        
        cfsetospeed(&tty, speed);
        cfsetispeed(&tty, speed);
        
        // 设置数据格式: 8N1
        tty.c_cflag &= ~PARENB;   // 无奇偶校验
        tty.c_cflag &= ~CSTOPB;   // 1个停止位
        tty.c_cflag &= ~CSIZE;    // 清除数据位设置
        tty.c_cflag |= CS8;       // 8个数据位
        tty.c_cflag &= ~CRTSCTS;  // 禁用硬件流控制
        tty.c_cflag |= CREAD | CLOCAL; // 启用接收器，忽略调制解调器控制线
        
        // 设置输入模式
        tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 禁用软件流控制
        tty.c_iflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始输入
        
        // 设置输出模式
        tty.c_oflag &= ~OPOST; // 原始输出
        
        // 设置等待时间和最小接收字符
        tty.c_cc[VTIME] = 10;    // 1秒超时
        tty.c_cc[VMIN] = 0;      // 非阻塞读取
        
        // 应用设置
        if (tcsetattr(serial_fd_, TCSANOW, &tty) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }
        
        return true;
    }
    
    // 发送原始数据
    bool sendRawData(const void* data, size_t size) {
        if (serial_fd_ < 0) {
            return false;
        }
        
        ssize_t bytes_written = write(serial_fd_, data, size);
        return bytes_written == static_cast<ssize_t>(size);
    }
    
    // 发送带协议的数据包
    template<typename T>
    bool sendPacket(const T& data) {
        if (serial_fd_ < 0) {
            return false;
        }
        
        // 创建发送缓冲区
        uint8_t buffer[256];
        size_t index = 0;
        
        // 添加帧头
        buffer[index++] = 0xA5;  // 帧头1
        buffer[index++] = 0x5A;  // 帧头2
        
        // 添加数据长度
        buffer[index++] = sizeof(T);
        
        // 添加数据内容
        memcpy(&buffer[index], &data, sizeof(T));
        index += sizeof(T);
        
        // 计算校验和
        uint8_t checksum = 0;
        for (size_t i = 2; i < index; i++) {
            checksum ^= buffer[i];
        }
        buffer[index++] = checksum;
        
        // 添加帧尾
        buffer[index++] = 0x0D;  // 帧尾1
        buffer[index++] = 0x0A;  // 帧尾2
        
        // 发送数据
        ssize_t bytes_written = write(serial_fd_, buffer, index);
        return bytes_written == static_cast<ssize_t>(index);
    }
    
    // 检查串口是否已初始化
    bool isInitialized() const {
        return serial_fd_ >= 0;
    }

private:
    int serial_fd_ = -1;  // 串口文件描述符
};
