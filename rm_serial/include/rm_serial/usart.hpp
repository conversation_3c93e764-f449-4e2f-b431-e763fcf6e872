#pragma once

#include <fcntl.h>
#include <termios.h>
#include <unistd.h>
#include <cstring>
#include <string>
#include <cstdint>
#include <mutex>

class usart_transmitter {
public:
    usart_transmitter() = default;
    
    ~usart_transmitter() {
        if (serial_fd_ >= 0) {
            close(serial_fd_);
        }
    }
    
    // 初始化串口
    bool init(const std::string& port, int baudrate = 115200) {
        std::lock_guard<std::mutex> lock(mutex_);

        serial_fd_ = open(port.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
        if (serial_fd_ < 0) {
            return false;
        }

        // 恢复串口为阻塞状态
        if (fcntl(serial_fd_, F_SETFL, 0) < 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }
        
        struct termios tty;
        if (tcgetattr(serial_fd_, &tty) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }
        
        // 设置波特率
        speed_t speed;
        switch (baudrate) {
            case 9600: speed = B9600; break;
            case 19200: speed = B19200; break;
            case 38400: speed = B38400; break;
            case 57600: speed = B57600; break;
            case 115200: speed = B115200; break;
            case 230400: speed = B230400; break;
            case 460800: speed = B460800; break;
            case 921600: speed = B921600; break;
            default: speed = B115200; break;
        }
        
        cfsetospeed(&tty, speed);
        cfsetispeed(&tty, speed);
        
        // 设置数据格式: 8N1
        tty.c_cflag &= ~PARENB;   // 无奇偶校验
        tty.c_cflag &= ~CSTOPB;   // 1个停止位
        tty.c_cflag &= ~CSIZE;    // 清除数据位设置
        tty.c_cflag |= CS8;       // 8个数据位
        tty.c_cflag &= ~CRTSCTS;  // 禁用硬件流控制
        tty.c_cflag |= CREAD | CLOCAL; // 启用接收器，忽略调制解调器控制线
        
        // 设置输入模式
        tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 禁用软件流控制
        tty.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP); // 原始输入模式

        // 设置本地模式
        tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始输入
        
        // 设置输出模式
        tty.c_oflag &= ~OPOST; // 原始输出
        
        // 设置等待时间和最小接收字符
        tty.c_cc[VTIME] = 1;     // 读取一个字符等待1*(1/10)s
        tty.c_cc[VMIN] = 1;      // 读取字符的最少个数为1

        // 清空输入缓冲区
        tcflush(serial_fd_, TCIFLUSH);

        // 应用设置
        if (tcsetattr(serial_fd_, TCSANOW, &tty) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }
        
        return true;
    }
    
    // 发送原始数据
    bool sendRawData(const void* data, size_t size) {
        std::lock_guard<std::mutex> lock(mutex_);

        if (serial_fd_ < 0) {
            return false;
        }

        ssize_t bytes_written = write(serial_fd_, data, size);
        return bytes_written == static_cast<ssize_t>(size);
    }
    
    // 发送带协议的数据包 (兼容FYT项目协议格式)
    template<typename T>
    bool sendPacket(const T& data) {
        std::lock_guard<std::mutex> lock(mutex_);

        if (serial_fd_ < 0) {
            return false;
        }

        // 创建发送缓冲区 (固定16字节格式)
        uint8_t buffer[16];
        memset(buffer, 0, sizeof(buffer));

        // 添加帧头
        buffer[0] = 0xFF;  // 帧头 (兼容FYT协议)

        // 添加数据内容 (从第1字节开始)
        if (sizeof(T) <= 14) {  // 确保不超出缓冲区
            memcpy(&buffer[1], &data, sizeof(T));
        } else {
            return false;  // 数据过大
        }

        // 计算校验和 (简单异或校验)
        uint8_t checksum = 0;
        for (size_t i = 1; i < 14; i++) {
            checksum ^= buffer[i];
        }
        buffer[14] = checksum;

        // 添加帧尾
        buffer[15] = 0x0D;  // 帧尾 (兼容FYT协议)

        // 发送数据
        ssize_t bytes_written = write(serial_fd_, buffer, sizeof(buffer));
        return bytes_written == static_cast<ssize_t>(sizeof(buffer));
    }
    
    // 检查串口是否已初始化
    bool isInitialized() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return serial_fd_ >= 0;
    }

    // 接收数据
    ssize_t receiveData(void* buffer, size_t size) {
        std::lock_guard<std::mutex> lock(mutex_);

        if (serial_fd_ < 0) {
            return -1;
        }

        return read(serial_fd_, buffer, size);
    }

private:
    int serial_fd_ = -1;           // 串口文件描述符
    mutable std::mutex mutex_;     // 线程安全互斥锁
};
