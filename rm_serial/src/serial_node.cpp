#include "rclcpp/rclcpp.hpp"
#include "rm_serial/serial_transmitter.hpp"

int main(int argc, char **argv) {
  rclcpp::init(argc, argv);
  auto node = std::make_shared<rclcpp::Node>("serial_node");

  std::string port = node->declare_parameter("port", "/dev/ttyUSB0");
  int baudrate = node->declare_parameter("baudrate", 115200);

  serialTransmitter transmitter(node, port, baudrate);
  transmitter.getSubscription_default();

  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}