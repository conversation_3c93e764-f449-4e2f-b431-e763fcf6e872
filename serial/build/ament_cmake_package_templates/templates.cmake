set(ament_cmake_package_templates_ENVIRONMENT_HOOK_LIBRARY_PATH "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/environment_hook/library_path.sh")
set(ament_cmake_package_templates_ENVIRONMENT_HOOK_PYTHONPATH "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/environment_hook/pythonpath.sh.in")
set(ament_cmake_package_templates_PACKAGE_LEVEL "")
list(APPEND ament_cmake_package_templates_PACKAGE_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/package_level/local_setup.bash.in")
list(APPEND ament_cmake_package_templates_PACKAGE_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/package_level/local_setup.sh.in")
list(APPEND ament_cmake_package_templates_PACKAGE_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/package_level/local_setup.zsh.in")
set(ament_cmake_package_templates_PREFIX_LEVEL "")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/local_setup.bash")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/local_setup.sh.in")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/local_setup.zsh")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/setup.bash")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/setup.sh.in")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/setup.zsh")
list(APPEND ament_cmake_package_templates_PREFIX_LEVEL "/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/prefix_level/_local_setup_util.py")
